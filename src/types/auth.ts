export interface ConfirmPasswordRequest extends PasswordConfirmationValues {
  token: string;
  email: string;
  password: string;
  passwordConfirmation: string;
}

export interface ConfirmPasswordResponse {
  message: string;
}

export interface PasswordConfirmationValues {
  password: string;
  passwordConfirmation: string;
}

export interface AuthRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  exp: number;
  iat: number;
  sub: string;
  username: string;
  roles: string[];
}
