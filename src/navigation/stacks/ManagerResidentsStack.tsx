import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { ManagerResidentsStackParamList } from "../types";
import { MANAGER_RESIDENTS_SCREENS } from "../constants";

// Screens - Placeholder components for now
import { ManagerResidentsListScreen } from "../../screens/Manager/ManagerResidentsListScreen";
import { ManagerResidentDetailScreen } from "../../screens/Manager/ManagerResidentDetailScreen";
import { AddResidentScreen } from "../../screens/Manager/AddResidentScreen";
import { EditResidentScreen } from "../../screens/Manager/EditResidentScreen";

const Stack = createNativeStackNavigator<ManagerResidentsStackParamList>();

export const ManagerResidentsStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={MANAGER_RESIDENTS_SCREENS.RESIDENTS_LIST}
        component={ManagerResidentsListScreen}
        options={{
          title: "Residentes",
        }}
      />
      <Stack.Screen
        name={MANAGER_RESIDENTS_SCREENS.RESIDENT_DETAIL}
        component={ManagerResidentDetailScreen}
        options={{
          title: "Detalle de Residente",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={MANAGER_RESIDENTS_SCREENS.ADD_RESIDENT}
        component={AddResidentScreen}
        options={{
          title: "Agregar Residente",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={MANAGER_RESIDENTS_SCREENS.EDIT_RESIDENT}
        component={EditResidentScreen}
        options={{
          title: "Editar Residente",
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
