import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { ManagerPropertiesStackParamList } from "../types";
import { MANAGER_PROPERTIES_SCREENS } from "../constants";

// Screens - Placeholder components for now
import { ManagerPropertiesListScreen } from "../../screens/Manager/ManagerPropertiesListScreen";
import { ManagerPropertyDetailScreen } from "../../screens/Manager/ManagerPropertyDetailScreen";
import { PropertyAnalyticsScreen } from "../../screens/Manager/PropertyAnalyticsScreen";

const Stack = createNativeStackNavigator<ManagerPropertiesStackParamList>();

export const ManagerPropertiesStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={MANAGER_PROPERTIES_SCREENS.PROPERTIES_LIST}
        component={ManagerPropertiesListScreen}
        options={{
          title: "Propiedades",
        }}
      />
      <Stack.Screen
        name={MANAGER_PROPERTIES_SCREENS.PROPERTY_DETAIL}
        component={ManagerPropertyDetailScreen}
        options={{
          title: "Detalle de Propiedad",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={MANAGER_PROPERTIES_SCREENS.PROPERTY_ANALYTICS}
        component={PropertyAnalyticsScreen}
        options={{
          title: "Analíticas de Propiedad",
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
