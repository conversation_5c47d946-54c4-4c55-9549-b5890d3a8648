import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { VisitsStackParamList } from "../types";
import { VISITS_SCREENS } from "../constants";

// Screens - Placeholder components for now
import { VisitsListScreen } from "../../screens/Security/VisitsListScreen";
import { VisitDetailScreen } from "../../screens/Security/VisitDetailScreen";
import { CreateVisitScreen } from "../../screens/Security/CreateVisitScreen";
import { CheckInVisitScreen } from "../../screens/Security/CheckInVisitScreen";
import { CheckOutVisitScreen } from "../../screens/Security/CheckOutVisitScreen";

const Stack = createNativeStackNavigator<VisitsStackParamList>();

export const VisitsStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={VISITS_SCREENS.VISITS_LIST}
        component={VisitsListScreen}
        options={{
          title: "Visitas",
        }}
      />
      <Stack.Screen
        name={VISITS_SCREENS.VISIT_DETAIL}
        component={VisitDetailScreen}
        options={{
          title: "Detalle de Visita",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={VISITS_SCREENS.CREATE_VISIT}
        component={CreateVisitScreen}
        options={{
          title: "Registrar Visita",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={VISITS_SCREENS.CHECK_IN_VISIT}
        component={CheckInVisitScreen}
        options={{
          title: "Check-in Visita",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={VISITS_SCREENS.CHECK_OUT_VISIT}
        component={CheckOutVisitScreen}
        options={{
          title: "Check-out Visita",
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
