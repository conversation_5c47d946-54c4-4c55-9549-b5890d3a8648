import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { ManagerReportsStackParamList } from "../types";
import { MANAGER_REPORTS_SCREENS } from "../constants";

// Screens - Placeholder components for now
import { ManagerReportsListScreen } from "../../screens/Manager/ManagerReportsListScreen";
import { FinancialReportsScreen } from "../../screens/Manager/FinancialReportsScreen";
import { OccupancyReportsScreen } from "../../screens/Manager/OccupancyReportsScreen";
import { MaintenanceReportsScreen } from "../../screens/Manager/MaintenanceReportsScreen";
import { ExportReportsScreen } from "../../screens/Manager/ExportReportsScreen";

const Stack = createNativeStackNavigator<ManagerReportsStackParamList>();

export const ManagerReportsStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={MANAGER_REPORTS_SCREENS.REPORTS_LIST}
        component={ManagerReportsListScreen}
        options={{
          title: "Reportes",
        }}
      />
      <Stack.Screen
        name={MANAGER_REPORTS_SCREENS.FINANCIAL_REPORTS}
        component={FinancialReportsScreen}
        options={{
          title: "Reportes Financieros",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={MANAGER_REPORTS_SCREENS.OCCUPANCY_REPORTS}
        component={OccupancyReportsScreen}
        options={{
          title: "Reportes de Ocupación",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={MANAGER_REPORTS_SCREENS.MAINTENANCE_REPORTS}
        component={MaintenanceReportsScreen}
        options={{
          title: "Reportes de Mantenimiento",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={MANAGER_REPORTS_SCREENS.EXPORT_REPORTS}
        component={ExportReportsScreen}
        options={{
          title: "Exportar Reportes",
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
