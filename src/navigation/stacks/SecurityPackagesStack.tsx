import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { SecurityPackagesStackParamList } from "../types";
import { SECURITY_PACKAGES_SCREENS } from "../constants";

// Screens - Placeholder components for now
import { SecurityPackagesListScreen } from "../../screens/Security/SecurityPackagesListScreen";
import { SecurityPackageDetailScreen } from "../../screens/Security/SecurityPackageDetailScreen";
import { ReceivePackageScreen } from "../../screens/Security/ReceivePackageScreen";
import { DeliverPackageScreen } from "../../screens/Security/DeliverPackageScreen";

const Stack = createNativeStackNavigator<SecurityPackagesStackParamList>();

export const SecurityPackagesStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={SECURITY_PACKAGES_SCREENS.PACKAGES_LIST}
        component={SecurityPackagesListScreen}
        options={{
          title: "Paquetes",
        }}
      />
      <Stack.Screen
        name={SECURITY_PACKAGES_SCREENS.PACKAGE_DETAIL}
        component={SecurityPackageDetailScreen}
        options={{
          title: "Detalle de Paquete",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={SECURITY_PACKAGES_SCREENS.RECEIVE_PACKAGE}
        component={ReceivePackageScreen}
        options={{
          title: "Recibir Paquete",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={SECURITY_PACKAGES_SCREENS.DELIVER_PACKAGE}
        component={DeliverPackageScreen}
        options={{
          title: "Entregar Paquete",
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
