import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { IncidentsStackParamList } from "../types";
import { INCIDENTS_SCREENS } from "../constants";

// Screens - Placeholder components for now
import { IncidentsListScreen } from "../../screens/Security/IncidentsListScreen";
import { IncidentDetailScreen } from "../../screens/Security/IncidentDetailScreen";
import { CreateIncidentScreen } from "../../screens/Security/CreateIncidentScreen";

const Stack = createNativeStackNavigator<IncidentsStackParamList>();

export const IncidentsStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={INCIDENTS_SCREENS.INCIDENTS_LIST}
        component={IncidentsListScreen}
        options={{
          title: "Incidentes",
        }}
      />
      <Stack.Screen
        name={INCIDENTS_SCREENS.INCIDENT_DETAIL}
        component={IncidentDetailScreen}
        options={{
          title: "Detalle de Incidente",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={INCIDENTS_SCREENS.CREATE_INCIDENT}
        component={CreateIncidentScreen}
        options={{
          title: "Crear Incidente",
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
