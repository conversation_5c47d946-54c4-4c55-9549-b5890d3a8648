import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { SecurityDashboardStackParamList } from "../types";
import { SECURITY_DASHBOARD_SCREENS } from "../constants";

// Screens - Placeholder components for now
import { SecurityDashboardScreen } from "../../screens/Security/SecurityDashboardScreen";
import { EmergencyNumbersScreen } from "../../screens/Dashboard/EmergencyNumbersScreen";

const Stack = createNativeStackNavigator<SecurityDashboardStackParamList>();

export const SecurityDashboardStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={SECURITY_DASHBOARD_SCREENS.DASHBOARD}
        component={SecurityDashboardScreen}
        options={{
          title: "Dashboard de Seguridad",
        }}
      />
      <Stack.Screen
        name={SECURITY_DASHBOARD_SCREENS.EMERGENCY_NUMBERS}
        component={EmergencyNumbersScreen}
        options={{
          title: "Números de Emergencia",
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
