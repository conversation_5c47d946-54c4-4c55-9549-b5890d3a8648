import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { AccountScreen } from "../../screens/Account/AccountScreen";
import { AccountStackParamList } from "../types";
import { ACCOUNT_SCREENS } from "../constants";
import { NotificationsConfig } from "../../screens/Account/NotificationsConfig";

const Stack = createNativeStackNavigator<AccountStackParamList>();

export const AccountStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={ACCOUNT_SCREENS.PROFILE}
        component={AccountScreen}
        options={{
          title: "Mi Cuenta",
        }}
      />
      <Stack.Screen
        name={ACCOUNT_SCREENS.NOTIFICATIONS_CONFIG}
        component={NotificationsConfig}
        options={{
          title: "Configuración de Notificaciones",
          headerShown: false,
        }}
      />
      {/* Future account screens can be added here */}
      {/* <Stack.Screen
        name={ACCOUNT_SCREENS.SETTINGS}
        component={SettingsScreen}
        options={{
          title: "Configuración",
          headerShown: true,
        }}
      />
      <Stack.Screen
        name={ACCOUNT_SCREENS.EDIT_PROFILE}
        component={EditProfileScreen}
        options={{
          title: "Editar Perfil",
          headerShown: true,
        }}
      /> */}
    </Stack.Navigator>
  );
};
