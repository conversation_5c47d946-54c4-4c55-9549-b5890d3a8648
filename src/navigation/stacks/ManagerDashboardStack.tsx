import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { ManagerDashboardStackParamList } from "../types";
import { MANAGER_DASHBOARD_SCREENS } from "../constants";

// Screens - Placeholder components for now
import { ManagerDashboardScreen } from "../../screens/Manager/ManagerDashboardScreen";
import { AnalyticsScreen } from "../../screens/Manager/AnalyticsScreen";
import { EmergencyNumbersScreen } from "../../screens/Dashboard/EmergencyNumbersScreen";

const Stack = createNativeStackNavigator<ManagerDashboardStackParamList>();

export const ManagerDashboardStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={MANAGER_DASHBOARD_SCREENS.DASHBOARD}
        component={ManagerDashboardScreen}
        options={{
          title: "Dashboard de Administración",
        }}
      />
      <Stack.Screen
        name={MANAGER_DASHBOARD_SCREENS.ANALYTICS}
        component={AnalyticsScreen}
        options={{
          title: "Analíticas",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={MANAGER_DASHBOARD_SCREENS.EMERGENCY_NUMBERS}
        component={EmergencyNumbersScreen}
        options={{
          title: "Números de Emergencia",
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
