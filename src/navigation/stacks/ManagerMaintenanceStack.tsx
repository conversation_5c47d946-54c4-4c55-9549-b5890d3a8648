import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { ManagerMaintenanceStackParamList } from "../types";
import { MANAGER_MAINTENANCE_SCREENS } from "../constants";

// Screens - Placeholder components for now
import { ManagerMaintenanceListScreen } from "../../screens/Manager/ManagerMaintenanceListScreen";
import { ManagerMaintenanceDetailScreen } from "../../screens/Manager/ManagerMaintenanceDetailScreen";
import { AssignMaintenanceScreen } from "../../screens/Manager/AssignMaintenanceScreen";
import { MaintenanceAnalyticsScreen } from "../../screens/Manager/MaintenanceAnalyticsScreen";

const Stack = createNativeStackNavigator<ManagerMaintenanceStackParamList>();

export const ManagerMaintenanceStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={MANAGER_MAINTENANCE_SCREENS.MAINTENANCE_LIST}
        component={ManagerMaintenanceListScreen}
        options={{
          title: "Mantenimiento",
        }}
      />
      <Stack.Screen
        name={MANAGER_MAINTENANCE_SCREENS.MAINTENANCE_DETAIL}
        component={ManagerMaintenanceDetailScreen}
        options={{
          title: "Detalle de Mantenimiento",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={MANAGER_MAINTENANCE_SCREENS.ASSIGN_MAINTENANCE}
        component={AssignMaintenanceScreen}
        options={{
          title: "Asignar Mantenimiento",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={MANAGER_MAINTENANCE_SCREENS.MAINTENANCE_ANALYTICS}
        component={MaintenanceAnalyticsScreen}
        options={{
          title: "Analíticas de Mantenimiento",
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
