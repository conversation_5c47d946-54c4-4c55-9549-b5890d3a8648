import { NavigatorScreenParams, RouteProp } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Facility, PartialFacility } from "../interfaces/facility";
import { Fine } from "../interfaces/fine";
import { Infraction } from "../interfaces/infraction";
import { Complaint } from "../interfaces/complaint";
import { Reservation } from "../interfaces/reservation";
import { MaintenanceIssueReport } from "../interfaces/maintenance-issue-report";
import { MonthlyMaintenanceCharge } from "../interfaces/monthly-maintenance-charge";
import { Package } from "../interfaces/package";
import { Property } from "../interfaces/property";

// Root Stack Parameter List
export type RootStackParamList = {
  Auth: undefined;
  Main: NavigatorScreenParams<MainTabParamList>;
  ValidateEmail: undefined;
  ValidateToken: { email: string };
  ConfirmPassword: { token: string; email: string };
};

// Stack Parameter Lists
export type DashboardStackParamList = {
  Dashboard: undefined;
  CreateComplaint: { complaintId?: string };
  CreateMaintenanceReport: { reportId?: string };
  EmergencyNumbers: undefined;
};

export type FacilitiesStackParamList = {
  FacilitiesList: { isLoading?: boolean };
  FacilityDetail: { facility: PartialFacility };
  CreateReservation: {
    id: Facility["id"];
    name: Facility["name"];
    maxAmountOfPeople: Facility["maxAmountOfPeople"];
    maxTimeOfStay: Facility["maxTimeOfStay"];
    open: Facility["open"];
    close: Facility["close"];
    selectedDate: string;
  };
};

export type PropertyStackParamList = {
  PropertyDetail: undefined;
  PropertyFines: { fines: Fine[]; property: Property };
  PropertyInfractions: { infractions: Infraction[]; property: Property };
  PropertyReservations: {
    reservations: Reservation[];
    property: Property;
  };
  PropertyMaintenanceReports: {
    maintenanceIssueReports: MaintenanceIssueReport[];
    property: Property;
  };
  PropertyComplaints: { complaints: Complaint[]; property: Property };
  PropertyTags: { propertyId: Property["id"] };
  PropertyResidents: {
    propertyId: Property["id"];
  };
  PropertyVehicles: { propertyId: Property["id"] };
  PropertyPets: { propertyId: Property["id"] };
  PropertyParkingSpots: {
    propertyId: Property["id"];
  };
  PropertyMonthlyCharges: { filterPaid?: boolean };
  PropertyPackages: { packages: Package[]; property: Property };

  // Detail screens
  ReservationDetail: { reservationId: Reservation["id"] };
  FineDetail: { fineId: Fine["id"] };
  InfractionDetail: { infractionId: Infraction["id"] };
  MonthlyChargeDetail: { charge: MonthlyMaintenanceCharge };
  ComplaintDetail: { complaintId: Complaint["id"] };
  MaintenanceIssueReportDetail: {
    maintenanceIssueReportId: MaintenanceIssueReport["id"];
  };
  PackageDetail: { packageId: Package["id"] };
};

export type PaymentsStackParamList = {
  PaymentsList: undefined;
  PaymentDetail: { paymentId: string };
};

export type AccountStackParamList = {
  Profile: undefined;
  Settings: undefined;
  EditProfile: undefined;
  NotificationsConfig: undefined;
};

// Security Guard Stack Parameter Lists
export type SecurityDashboardStackParamList = {
  SecurityDashboard: undefined;
  EmergencyNumbers: undefined;
};

export type VisitsStackParamList = {
  VisitsList: undefined;
  VisitDetail: { visitId: string };
  CreateVisit: undefined;
  CheckInVisit: { visitId: string };
  CheckOutVisit: { visitId: string };
};

export type SecurityPackagesStackParamList = {
  SecurityPackagesList: undefined;
  SecurityPackageDetail: { packageId: string };
  ReceivePackage: undefined;
  DeliverPackage: { packageId: string };
};

export type IncidentsStackParamList = {
  IncidentsList: undefined;
  IncidentDetail: { incidentId: string };
  CreateIncident: undefined;
};

// Property Manager Stack Parameter Lists
export type ManagerDashboardStackParamList = {
  ManagerDashboard: undefined;
  Analytics: undefined;
  EmergencyNumbers: undefined;
};

export type ManagerPropertiesStackParamList = {
  ManagerPropertiesList: undefined;
  ManagerPropertyDetail: { propertyId: string };
  PropertyAnalytics: { propertyId: string };
};

export type ManagerResidentsStackParamList = {
  ManagerResidentsList: undefined;
  ManagerResidentDetail: { residentId: string };
  AddResident: undefined;
  EditResident: { residentId: string };
};

export type ManagerMaintenanceStackParamList = {
  ManagerMaintenanceList: undefined;
  ManagerMaintenanceDetail: { maintenanceId: string };
  AssignMaintenance: { maintenanceId: string };
  MaintenanceAnalytics: undefined;
};

export type ManagerReportsStackParamList = {
  ManagerReportsList: undefined;
  FinancialReports: undefined;
  OccupancyReports: undefined;
  MaintenanceReports: undefined;
  ExportReports: undefined;
};

// Main Tab Navigator Parameter List - Default (Residents)
export type MainTabParamList = {
  DashboardTab: NavigatorScreenParams<DashboardStackParamList>;
  FacilitiesTab: NavigatorScreenParams<FacilitiesStackParamList>;
  PropertyTab: NavigatorScreenParams<PropertyStackParamList>;
  PaymentsTab: NavigatorScreenParams<PaymentsStackParamList>;
  AccountTab: NavigatorScreenParams<AccountStackParamList>;
};

// Security Guard Tab Navigator Parameter List
export type SecurityTabParamList = {
  SecurityDashboardTab: NavigatorScreenParams<SecurityDashboardStackParamList>;
  VisitsTab: NavigatorScreenParams<VisitsStackParamList>;
  PackagesTab: NavigatorScreenParams<SecurityPackagesStackParamList>;
  IncidentsTab: NavigatorScreenParams<IncidentsStackParamList>;
  AccountTab: NavigatorScreenParams<AccountStackParamList>;
};

// Property Manager Tab Navigator Parameter List
export type ManagerTabParamList = {
  ManagerDashboardTab: NavigatorScreenParams<ManagerDashboardStackParamList>;
  PropertiesTab: NavigatorScreenParams<ManagerPropertiesStackParamList>;
  ResidentsTab: NavigatorScreenParams<ManagerResidentsStackParamList>;
  MaintenanceTab: NavigatorScreenParams<ManagerMaintenanceStackParamList>;
  ReportsTab: NavigatorScreenParams<ManagerReportsStackParamList>;
  AccountTab: NavigatorScreenParams<AccountStackParamList>;
};

// Specific Route Types
export type FacilityRouteProp = RouteProp<
  FacilitiesStackParamList,
  "FacilityDetail"
>;

export type CreateReservationRouteProp = RouteProp<
  FacilitiesStackParamList,
  "CreateReservation"
>;

export type PropertyComplaintsRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyComplaints"
>;

export type PropertyFinesRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyFines"
>;

export type PropertyInfractionsRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyInfractions"
>;

export type PropertyReservationsRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyReservations"
>;

export type PropertyMaintenanceReportsRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyMaintenanceReports"
>;

export type PropertyMonthlyChargesRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyMonthlyCharges"
>;

export type PropertyResidentsRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyResidents"
>;

export type PropertyVehiclesRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyVehicles"
>;

export type PropertyPetsRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyPets"
>;

export type PropertyParkingSpotsRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyParkingSpots"
>;

export type PropertyPackagesRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyPackages"
>;

// Detail screen route props
export type ReservationDetailRouteProp = RouteProp<
  PropertyStackParamList,
  "ReservationDetail"
>;

export type FineDetailRouteProp = RouteProp<
  PropertyStackParamList,
  "FineDetail"
>;

export type InfractionDetailRouteProp = RouteProp<
  PropertyStackParamList,
  "InfractionDetail"
>;

export type MonthlyChargeDetailRouteProp = RouteProp<
  PropertyStackParamList,
  "MonthlyChargeDetail"
>;

export type ComplaintDetailRouteProp = RouteProp<
  PropertyStackParamList,
  "ComplaintDetail"
>;

export type MaintenanceIssueReportDetailRouteProp = RouteProp<
  PropertyStackParamList,
  "MaintenanceIssueReportDetail"
>;

export type PackageDetailRouteProp = RouteProp<
  PropertyStackParamList,
  "PackageDetail"
>;

export type ConfirmPasswordRouteProp = RouteProp<
  RootStackParamList,
  "ConfirmPassword"
>;

export type ValidateEmailRouteProp = RouteProp<
  RootStackParamList,
  "ValidateEmail"
>;

export type ValidateTokenRouteProp = RouteProp<
  RootStackParamList,
  "ValidateToken"
>;

export type Auth = RouteProp<RootStackParamList, "Auth">;

// Navigation Props
export type RootStackNavigationProp =
  NativeStackNavigationProp<RootStackParamList>;
export type PropertyStackNavigationProp =
  NativeStackNavigationProp<PropertyStackParamList>;
export type FacilitiesStackNavigationProp =
  NativeStackNavigationProp<FacilitiesStackParamList>;
export type DashboardStackNavigationProp =
  NativeStackNavigationProp<DashboardStackParamList>;
export type PaymentsStackNavigationProp =
  NativeStackNavigationProp<PaymentsStackParamList>;
export type AccountStackNavigationProp =
  NativeStackNavigationProp<AccountStackParamList>;
