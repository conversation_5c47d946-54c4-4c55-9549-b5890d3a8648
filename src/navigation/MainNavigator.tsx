import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { TabNavigator } from "./TabNavigator";
import { SecurityTabNavigator } from "./SecurityTabNavigator";
import { ManagerTabNavigator } from "./ManagerTabNavigator";
import { useAuthContext } from "../context/AuthContext";
import { getUserPrimaryRole } from "../utils/roleUtils";
import { USER_ROLES } from "./constants";

const Stack = createNativeStackNavigator();

export function MainNavigator() {
  const { user } = useAuthContext();
  const userRole = getUserPrimaryRole(user);

  // Determinar qué navegador usar según el rol
  const getNavigatorComponent = () => {
    switch (userRole) {
      case USER_ROLES.SECURITY_GUARD:
        return SecurityTabNavigator;
      case USER_ROLES.PROPERTY_MANAGER:
        return ManagerTabNavigator;
      case USER_ROLES.ADMIN:
        return ManagerTabNavigator; // Los admins usan la misma interfaz que los property managers
      default:
        return TabNavigator; // Residentes (Owner/Tenant)
    }
  };

  const NavigatorComponent = getNavigatorComponent();

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Tabs" component={NavigatorComponent} />
    </Stack.Navigator>
  );
}
