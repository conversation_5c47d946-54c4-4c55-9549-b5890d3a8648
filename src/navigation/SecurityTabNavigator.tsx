import {
  BottomTabNavigationOptions,
  createBottomTabNavigator,
} from "@react-navigation/bottom-tabs";
import {
  getFocusedRouteNameFromRoute,
  ParamListBase,
  RouteProp,
} from "@react-navigation/native";
import { useIsFetching } from "@tanstack/react-query";
import { LinearGradient } from "expo-linear-gradient";

// Stacks
import { SecurityDashboardStack } from "./stacks/SecurityDashboardStack";
import { VisitsStack } from "./stacks/VisitsStack";
import { SecurityPackagesStack } from "./stacks/SecurityPackagesStack";
import { IncidentsStack } from "./stacks/IncidentsStack";
import { AccountStack } from "./stacks/AccountStack";

// Types and Constants
import { SecurityTabParamList } from "./types";
import { 
  SECURITY_TAB_NAMES, 
  SECURITY_TAB_LABELS, 
  SECURITY_TAB_ICONS,
  HIDDEN_TAB_BAR_ROUTES 
} from "./constants";

// Components
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../theme";

const Tab = createBottomTabNavigator<SecurityTabParamList>();

const shouldHideTabBar = (route: RouteProp<ParamListBase, string>): boolean => {
  const routeName = getFocusedRouteNameFromRoute(route);
  return HIDDEN_TAB_BAR_ROUTES.includes(routeName as any);
};

const getScreenOptions = (
  route: RouteProp<ParamListBase, string>,
  isFetching: boolean
): BottomTabNavigationOptions => {
  const routeName = route.name as keyof typeof SECURITY_TAB_ICONS;
  const iconName = SECURITY_TAB_ICONS[routeName];
  const label = SECURITY_TAB_LABELS[routeName];

  return {
    tabBarIcon: ({ focused, color, size }) => (
      <MaterialCommunityIcons
        name={iconName}
        size={size}
        color={focused ? theme.colors.primaryDark : color}
      />
    ),
    tabBarLabel: label,
    tabBarStyle: {
      display: shouldHideTabBar(route) ? "none" : "flex",
      backgroundColor: "transparent",
      borderTopWidth: 0,
      elevation: 0,
      shadowOpacity: 0,
      position: "absolute",
      bottom: 0,
      left: 0,
      right: 0,
      height: 90,
      paddingBottom: 20,
      paddingTop: 10,
    },
    tabBarBackground: () => (
      <LinearGradient
        colors={[theme.colors.primary, theme.colors.primaryDark]}
        style={{
          flex: 1,
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
        }}
      />
    ),
    tabBarLabelStyle: {
      fontSize: 12,
      fontWeight: "600",
      marginTop: 4,
    },
    tabBarActiveTintColor: theme.colors.white,
    tabBarInactiveTintColor: theme.colors.gray300,
    headerShown: false,
  };
};

export const SecurityTabNavigator: React.FC = () => {
  const isFetching = useIsFetching();

  return (
    <Tab.Navigator
      screenOptions={({ route }: any) =>
        getScreenOptions(route, isFetching > 0)
      }
      initialRouteName={SECURITY_TAB_NAMES.DASHBOARD}
    >
      <Tab.Screen 
        name={SECURITY_TAB_NAMES.DASHBOARD} 
        component={SecurityDashboardStack} 
      />
      <Tab.Screen 
        name={SECURITY_TAB_NAMES.VISITS} 
        component={VisitsStack} 
      />
      <Tab.Screen 
        name={SECURITY_TAB_NAMES.PACKAGES} 
        component={SecurityPackagesStack} 
      />
      <Tab.Screen 
        name={SECURITY_TAB_NAMES.INCIDENTS} 
        component={IncidentsStack} 
      />
      <Tab.Screen 
        name={SECURITY_TAB_NAMES.ACCOUNT} 
        component={AccountStack} 
      />
    </Tab.Navigator>
  );
};
