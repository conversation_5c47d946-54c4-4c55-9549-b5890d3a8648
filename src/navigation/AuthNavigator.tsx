import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { LoginScreen } from "../screens/Auth/LoginScreen";
import { ValidateEmailScreen } from "../screens/Auth/ValidateEmailScreen";
import { ConfirmPasswordScreen } from "../screens/Auth/ConfirmPasswordScreen";
import { ValidateTokenScreen } from "../screens/Auth/ValidateTokenScreen";

const Stack = createNativeStackNavigator();

export const AuthNavigator: React.FC = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="ValidateEmail" component={ValidateEmailScreen} />
      <Stack.Screen name="ValidateToken" component={ValidateTokenScreen} />
      <Stack.Screen name="ConfirmPassword" component={ConfirmPasswordScreen} />
    </Stack.Navigator>
  );
};
