import {
  BottomTabNavigationOptions,
  createBottomTabNavigator,
} from "@react-navigation/bottom-tabs";
import {
  getFocusedRouteNameFromRoute,
  ParamListBase,
  RouteProp,
} from "@react-navigation/native";
import { useIsFetching } from "@tanstack/react-query";
import { LinearGradient } from "expo-linear-gradient";

// Stacks

import { AccountStack } from "./stacks/AccountStack";

// Types and Constants
import { ManagerTabParamList } from "./types";
import {
  MANAGER_TAB_NAMES,
  MANAGER_TAB_LABELS,
  MANAGER_TAB_ICONS,
  HIDDEN_TAB_BAR_ROUTES,
} from "./constants";

// Components
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../theme";
import { ManagerDashboardStack } from "./stacks/ManagerDashboardStack";
import { ManagerPropertiesStack } from "./stacks/ManagerPropertiesStack";
import { ManagerResidentsStack } from "./stacks/ManagerResidentsStack";
import { ManagerMaintenanceStack } from "./stacks/ManagerMaintenanceStack";
import { ManagerReportsStack } from "./stacks/ManagerReportsStack";

const Tab = createBottomTabNavigator<ManagerTabParamList>();

const shouldHideTabBar = (route: RouteProp<ParamListBase, string>): boolean => {
  const routeName = getFocusedRouteNameFromRoute(route);
  return HIDDEN_TAB_BAR_ROUTES.includes(routeName as any);
};

const getScreenOptions = (
  route: RouteProp<ParamListBase, string>,
  isFetching: boolean
): BottomTabNavigationOptions => {
  const routeName = route.name as keyof typeof MANAGER_TAB_ICONS;
  const iconName = MANAGER_TAB_ICONS[routeName];
  const label = MANAGER_TAB_LABELS[routeName];

  return {
    tabBarIcon: ({ focused, color, size }) => (
      <MaterialCommunityIcons
        name={iconName}
        size={size}
        color={focused ? theme.colors.primaryDark : color}
      />
    ),
    tabBarLabel: label,
    tabBarStyle: {
      display: shouldHideTabBar(route) ? "none" : "flex",
      backgroundColor: "transparent",
      borderTopWidth: 0,
      elevation: 0,
      shadowOpacity: 0,
      position: "absolute",
      bottom: 0,
      left: 0,
      right: 0,
      height: 90,
      paddingBottom: 20,
      paddingTop: 10,
    },
    tabBarBackground: () => (
      <LinearGradient
        colors={[theme.colors.secondary, theme.colors.secondaryDark]}
        style={{
          flex: 1,
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
        }}
      />
    ),
    tabBarLabelStyle: {
      fontSize: 12,
      fontWeight: "600",
      marginTop: 4,
    },
    tabBarActiveTintColor: theme.colors.white,
    tabBarInactiveTintColor: theme.colors.gray300,
    headerShown: false,
  };
};

export const ManagerTabNavigator: React.FC = () => {
  const isFetching = useIsFetching();

  return (
    <Tab.Navigator
      screenOptions={({ route }: any) =>
        getScreenOptions(route, isFetching > 0)
      }
      initialRouteName={MANAGER_TAB_NAMES.DASHBOARD}
    >
      <Tab.Screen
        name={MANAGER_TAB_NAMES.DASHBOARD}
        component={ManagerDashboardStack}
      />
      <Tab.Screen
        name={MANAGER_TAB_NAMES.PROPERTIES}
        component={ManagerPropertiesStack}
      />
      <Tab.Screen
        name={MANAGER_TAB_NAMES.RESIDENTS}
        component={ManagerResidentsStack}
      />
      <Tab.Screen
        name={MANAGER_TAB_NAMES.MAINTENANCE}
        component={ManagerMaintenanceStack}
      />
      <Tab.Screen
        name={MANAGER_TAB_NAMES.REPORTS}
        component={ManagerReportsStack}
      />
      <Tab.Screen name={MANAGER_TAB_NAMES.ACCOUNT} component={AccountStack} />
    </Tab.Navigator>
  );
};
