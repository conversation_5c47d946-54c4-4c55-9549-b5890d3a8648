import React, { useEffect } from "react";
import { useNavigation, CommonActions } from "@react-navigation/native";
import { useNotifications } from "../hooks/useNotifications";
import { notificationService } from "../services/notificationService";
import {
  TAB_NAMES,
  PROPERTY_SCREENS,
  DASHBOARD_SCREENS,
} from "../navigation/constants";

interface NotificationHandlerProps {
  children: React.ReactNode;
}

/**
 * Estructura de datos esperada en las notificaciones push
 * Esta interfaz documenta el formato esperado, pero la función usa 'any'
 * para mayor flexibilidad con datos del backend
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface NotificationData {
  type: "fine" | "package" | "infraction" | "announcement" | "comunicado";
  fineId?: string;
  packageId?: string;
  infractionId?: string;
  // Campos adicionales que pueden venir del backend
  title?: string;
  message?: string;
  action?: string;
}

/**
 * Componente que maneja las notificaciones globalmente en la aplicación
 * Debe estar dentro del NavigationContainer para acceder al contexto de navegación
 * Actualmente se usa en AppNavigator.tsx
 */
export const NotificationHandler: React.FC<NotificationHandlerProps> = ({
  children,
}) => {
  const navigation = useNavigation();
  const {
    lastNotification,
    lastNotificationResponse,
    clearBadgeAndNotifications,
  } = useNotifications();

  /**
   * Maneja la navegación basada en el tipo de notificación
   */
  const handleNotificationNavigation = (data: any) => {
    if (!data?.type) {
      console.log("No notification type specified");
      return;
    }

    try {
      switch (data.type) {
        case "fine":
          // Navegar al detalle de multa
          if (data.fineId) {
            navigation.dispatch(
              CommonActions.navigate({
                name: TAB_NAMES.PROPERTY,
                params: {
                  screen: PROPERTY_SCREENS.FINE_DETAIL,
                  params: { fineId: data.fineId },
                },
              })
            );
          }
          break;

        case "package":
          // Navegar al detalle de paquete
          if (data.packageId) {
            navigation.dispatch(
              CommonActions.navigate({
                name: TAB_NAMES.PROPERTY,
                params: {
                  screen: PROPERTY_SCREENS.PACKAGE_DETAIL,
                  params: { packageId: data.packageId },
                },
              })
            );
          }
          break;

        case "infraction":
          // Navegar al detalle de infracción
          if (data.infractionId) {
            navigation.dispatch(
              CommonActions.navigate({
                name: TAB_NAMES.PROPERTY,
                params: {
                  screen: PROPERTY_SCREENS.INFRACTION_DETAIL,
                  params: { infractionId: data.infractionId },
                },
              })
            );
          }
          break;

        case "announcement":
          // Navegar al dashboard para comunicados
          navigation.dispatch(
            CommonActions.navigate({
              name: TAB_NAMES.DASHBOARD,
              params: {
                screen: DASHBOARD_SCREENS.DASHBOARD,
              },
            })
          );
          break;

        default:
          console.log(`Unknown notification type: ${data.type}`);
          break;
      }
    } catch (error) {
      console.error("Error navigating from notification:", error);
    }
  };

  useEffect(() => {
    // Configurar el canal de Android al iniciar la app
    notificationService.setupAndroidChannel();
  }, []);

  useEffect(() => {
    if (lastNotification) {
      console.log("New notification received:", lastNotification);

      // Aquí puedes agregar lógica personalizada para manejar notificaciones
      // Por ejemplo, actualizar el estado de la app, mostrar alertas, etc.

      const { title, body, data } = lastNotification.request.content;
      console.log(`Notification: ${title} - ${body}`, data);
    }
  }, [lastNotification]);

  useEffect(() => {
    if (lastNotificationResponse) {
      console.log("Notification tapped:", lastNotificationResponse);

      // Manejar la navegación basada en la notificación
      const { data } = lastNotificationResponse.notification.request.content;

      // Usar la nueva función de navegación específica
      handleNotificationNavigation(data);

      // Limpiar notificaciones cuando el usuario interactúa con ellas
      clearBadgeAndNotifications();
    }
  }, [
    lastNotificationResponse,
    clearBadgeAndNotifications,
    handleNotificationNavigation,
  ]);

  return <>{children}</>;
};
