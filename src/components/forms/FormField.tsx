import { useState } from "react";
import {
  TextInput,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInputProps,
} from "react-native";
import { Controller, Control, FieldValues, FieldPath } from "react-hook-form";
import { Ionicons } from "@expo/vector-icons"; // Usa otro set si lo prefieres
import { theme } from "../../theme";

interface FormFieldProps<T extends FieldValues> {
  name: FieldPath<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  secureTextEntry?: boolean;
  keyboardType?: TextInputProps["keyboardType"];
  autoCapitalize?: TextInputProps["autoCapitalize"];
  icon?: React.ReactNode;
  disabled?: boolean;
  onPress?: () => void;
  hidden?: boolean;
}

export const FormField = <T extends FieldValues>({
  name,
  control,
  label,
  placeholder,
  secureTextEntry,
  keyboardType,
  autoCapitalize = "none",
  icon,
  disabled,
  onPress,
  hidden = false,
}: FormFieldProps<T>) => {
  const [showPassword, setShowPassword] = useState(false);

  if (hidden) {
    return null;
  }

  return (
    <Controller
      name={name}
      control={control}
      disabled={disabled}
      render={({
        field: { value, onChange, onBlur },
        fieldState: { error },
      }) => (
        <View style={styles.container}>
          {label && <Text style={styles.label}>{label}</Text>}

          <View
            style={[
              styles.inputContainer,
              !!error && styles.inputContainerError,
              disabled && styles.inputContainerDisabled,
            ]}
          >
            {icon && <View style={styles.iconContainer}>{icon}</View>}

            {onPress ? (
              <TouchableOpacity
                style={styles.input}
                onPress={onPress}
                activeOpacity={0.7}
                disabled={disabled}
              >
                <Text
                  style={{
                    color: disabled || !value ? "#999" : "black",
                    fontSize: theme.fontSizes.md,
                  }}
                >
                  {value ?? placeholder}
                </Text>
              </TouchableOpacity>
            ) : (
              <TextInput
                style={styles.input}
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                placeholder={placeholder}
                secureTextEntry={secureTextEntry && !showPassword}
                keyboardType={keyboardType}
                autoCapitalize={autoCapitalize}
                editable={!disabled}
              />
            )}

            {secureTextEntry && (
              <TouchableOpacity
                onPress={() => setShowPassword((prev) => !prev)}
                disabled={disabled}
              >
                <Ionicons
                  name={showPassword ? "eye-off" : "eye"}
                  size={theme.fontSizes.lg}
                  color={disabled ? "#ccc" : theme.colors.gray100}
                />
              </TouchableOpacity>
            )}
          </View>

          {error?.message && (
            <Text style={styles.errorText}>{error.message}</Text>
          )}
        </View>
      )}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.md,
    width: "100%",
  },
  label: {
    fontWeight: "600",
    marginBottom: theme.spacing.sm,
    fontSize: theme.fontSizes.md,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: theme.radii.xl,
    paddingHorizontal: theme.spacing.md,
    backgroundColor: "#F0F0F0",
  },
  inputContainerError: {
    borderColor: theme.colors.error,
  },
  inputContainerDisabled: {
    backgroundColor: "#f5f5f5",
    opacity: 0.6,
  },
  input: {
    flex: 1,
    fontSize: theme.fontSizes.md,
    paddingVertical: 12,
  },
  iconContainer: {
    marginRight: theme.spacing.sm,
  },
  errorText: {
    color: theme.colors.error,
    fontSize: theme.fontSizes.xs,
    marginTop: theme.spacing.xs,
  },
});
