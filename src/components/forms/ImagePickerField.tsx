import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  ScrollView,
} from "react-native";
import { Controller, Control, FieldValues, FieldPath } from "react-hook-form";
import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import { SaveFormat, ImageManipulator } from "expo-image-manipulator";
import { theme } from "../../theme";
import { useState } from "react";

interface ImagePickerFieldProps<T extends FieldValues> {
  name: FieldPath<T>;
  control: Control<T>;
  label?: string;
  maxImages?: number;
}

export const ImagePickerField = <T extends FieldValues>({
  name,
  control,
  label = "Agregar imágenes",
  maxImages = 3,
}: ImagePickerFieldProps<T>) => {
  const [isLoading, setIsLoading] = useState(false);

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Permisos requeridos",
        "Necesitamos acceso a tu galería para seleccionar imágenes."
      );
      return false;
    }
    return true;
  };

  const compressImageToMax1Mb = async (uri: string) => {
    let quality = 0.85;
    let size = Infinity;
    let resizedUri;
    const maxSize = 300 * 1024;

    do {
      // 1. Resize y render
      let manip = await ImageManipulator.manipulate(uri)
        .resize({ width: 1024 })
        .renderAsync();

      // 2. Guardar con calidad actual
      let saved = await manip.saveAsync({
        format: SaveFormat.JPEG,
        compress: quality,
      });

      // 3. Medir el tamaño
      const response = await fetch(saved.uri);
      const blob = await response.blob();
      size = blob.size;
      resizedUri = saved.uri;

      quality -= 0.05;
    } while (size > maxSize && quality > 0.3);

    return resizedUri;
  };

  const pickImages = async (
    currentImages: string[],
    onChange: (images: string[]) => void
  ) => {
    if (currentImages.length >= maxImages) {
      Alert.alert(
        "Límite alcanzado",
        `Máximo ${maxImages} imágenes permitidas`
      );
      return;
    }

    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    setIsLoading(true);
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ["images"],
        allowsMultipleSelection: true,
        aspect: [4, 3],
      });

      if (!result.canceled && result.assets) {
        const selectedAssets = result.assets.slice(
          0,
          maxImages - currentImages.length
        );

        // Procesa y comprime cada imagen seleccionada
        const compressedUris: string[] = [];
        for (const asset of selectedAssets) {
          const uri = await compressImageToMax1Mb(asset.uri);
          compressedUris.push(uri);
        }

        onChange([...currentImages, ...compressedUris]);
      }
    } catch (error) {
      Alert.alert("Error", "No se pudieron seleccionar las imágenes");
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  const removeImage = (
    index: number,
    currentImages: string[],
    onChange: (images: string[]) => void
  ) => {
    const newImages = currentImages.filter((_, i) => i !== index);
    onChange(newImages);
  };

  return (
    <Controller
      name={name}
      control={control}
      defaultValue={[] as any}
      render={({ field: { value = [], onChange }, fieldState: { error } }) => (
        <View style={styles.container}>
          {label && <Text style={styles.label}>{label}</Text>}

          <TouchableOpacity
            style={[styles.addButton, error && styles.errorBorder]}
            onPress={() => pickImages(value, onChange)}
            disabled={isLoading || value.length >= maxImages}
          >
            <Ionicons
              name={isLoading ? "hourglass-outline" : "camera-outline"}
              size={theme.fontSizes.xl}
              color={theme.colors.primary}
            />
            <Text style={styles.addButtonText}>
              {isLoading
                ? "Cargando..."
                : value.length >= maxImages
                ? `Máximo ${maxImages} imágenes`
                : "Seleccionar imágenes"}
            </Text>
            <Text style={styles.counterText}>
              {value.length}/{maxImages}
            </Text>
          </TouchableOpacity>

          {value.length > 0 && (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.imagesContainer}
            >
              {value.map((imageUri: string, index: number) => (
                <View key={index} style={styles.imageWrapper}>
                  <Image source={{ uri: imageUri }} style={styles.image} />
                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => removeImage(index, value, onChange)}
                  >
                    <Ionicons
                      name="close-circle"
                      size={theme.fontSizes.lg}
                      color={theme.colors.error}
                    />
                  </TouchableOpacity>
                </View>
              ))}
            </ScrollView>
          )}

          {error?.message && (
            <Text style={styles.errorText}>{error.message}</Text>
          )}
        </View>
      )}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.md,
  },
  label: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.gray700,
    marginBottom: theme.spacing.sm,
  },
  addButton: {
    borderWidth: 2,
    borderColor: theme.colors.primary,
    borderStyle: "dashed",
    borderRadius: theme.radii.md,
    padding: theme.spacing.lg,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: theme.colors.primaryLight,
  },
  addButtonText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.primary,
    fontWeight: "600",
    marginTop: theme.spacing.xs,
  },
  counterText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginTop: theme.spacing.xs,
  },
  imagesContainer: {
    marginTop: theme.spacing.md,
  },
  imageWrapper: {
    position: "relative",
    marginRight: theme.spacing.sm,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: theme.radii.md,
  },
  removeButton: {
    position: "absolute",
    top: -8,
    right: -8,
    backgroundColor: theme.colors.white,
    borderRadius: 12,
  },
  errorBorder: {
    borderColor: theme.colors.error,
  },
  errorText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.error,
    marginTop: theme.spacing.xs,
  },
});
