import { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
} from "react-native";
import { Controller, Control, FieldValues, FieldPath } from "react-hook-form";
import { Ionicons } from "@expo/vector-icons";
import { theme } from "../../theme";

interface SelectOption {
  label: string;
  value: string;
}

interface SelectFieldProps<T extends FieldValues> {
  name: FieldPath<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  options: SelectOption[];
  disabled?: boolean;
}

export const SelectField = <T extends FieldValues>({
  name,
  control,
  label,
  placeholder = "Seleccionar...",
  options,
  disabled = false,
}: SelectFieldProps<T>) => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const openModal = () => {
    if (!disabled) {
      setIsModalVisible(true);
    }
  };

  const closeModal = () => {
    setIsModalVisible(false);
  };

  const selectOption = (
    option: SelectOption,
    onChange: (value: string) => void
  ) => {
    onChange(option.value);
    closeModal();
  };

  const getSelectedLabel = (value: string) => {
    const selectedOption = options.find((option) => option.value === value);
    return selectedOption?.label ?? placeholder;
  };

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { value, onChange }, fieldState: { error } }) => (
        <View style={styles.container}>
          {label && <Text style={styles.label}>{label}</Text>}

          <TouchableOpacity
            style={[
              styles.selectButton,
              error && styles.errorBorder,
              disabled && styles.disabledButton,
            ]}
            onPress={openModal}
            disabled={disabled}
          >
            <Text
              style={[
                styles.selectText,
                !value && styles.placeholderText,
                disabled && styles.disabledText,
              ]}
            >
              {getSelectedLabel(value)}
            </Text>
            <Ionicons
              name="chevron-down"
              size={theme.fontSizes.md}
              color={disabled ? theme.colors.gray500 : theme.colors.gray700}
            />
          </TouchableOpacity>

          {error?.message && (
            <Text style={styles.errorText}>{error.message}</Text>
          )}

          <Modal
            visible={isModalVisible}
            transparent
            animationType="fade"
            onRequestClose={closeModal}
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>
                    {label ?? "Seleccionar opción"}
                  </Text>
                  <TouchableOpacity onPress={closeModal}>
                    <Ionicons
                      name="close"
                      size={theme.fontSizes.lg}
                      color={theme.colors.gray700}
                    />
                  </TouchableOpacity>
                </View>

                <FlatList
                  data={options}
                  keyExtractor={(item) => item.value}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={[
                        styles.option,
                        value === item.value && styles.selectedOption,
                      ]}
                      onPress={() => selectOption(item, onChange)}
                    >
                      <Text
                        style={[
                          styles.optionText,
                          value === item.value && styles.selectedOptionText,
                        ]}
                      >
                        {item.label}
                      </Text>
                      {value === item.value && (
                        <Ionicons
                          name="checkmark"
                          size={theme.fontSizes.md}
                          color={theme.colors.primary}
                        />
                      )}
                    </TouchableOpacity>
                  )}
                  style={styles.optionsList}
                />
              </View>
            </View>
          </Modal>
        </View>
      )}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.md,
  },
  label: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.gray700,
    marginBottom: theme.spacing.sm,
  },
  selectButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderWidth: 1,
    borderColor: theme.colors.gray300,
    borderRadius: theme.radii.md,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.white,
  },
  selectText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray900,
    flex: 1,
  },
  placeholderText: {
    color: theme.colors.gray500,
  },
  disabledButton: {
    backgroundColor: theme.colors.gray100,
    borderColor: theme.colors.gray200,
  },
  disabledText: {
    color: theme.colors.gray500,
  },
  errorBorder: {
    borderColor: theme.colors.error,
  },
  errorText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.error,
    marginTop: theme.spacing.xs,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: theme.colors.white,
    borderTopLeftRadius: theme.radii.xl,
    borderTopRightRadius: theme.radii.xl,
    maxHeight: "70%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
  },
  modalTitle: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.gray900,
  },
  optionsList: {
    maxHeight: 300,
  },
  option: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray100,
  },
  selectedOption: {
    backgroundColor: theme.colors.primaryLight,
  },
  optionText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray700,
    flex: 1,
  },
  selectedOptionText: {
    color: theme.colors.primary,
    fontWeight: "600",
  },
});
