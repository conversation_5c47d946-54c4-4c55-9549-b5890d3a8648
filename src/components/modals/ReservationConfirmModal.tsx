import { View, Text, StyleSheet } from "react-native";
import Modal from "react-native-modal";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../theme";
import { DateTime } from "luxon";
import { But<PERSON>, Col, Row } from "../main";
import { DropDown } from "../Dropdown";

interface ReservationData {
  facilityName: string;
  amountOfPeople: number;
  startDateTime: string;
  endDateTime: string;
  selectedDate: string;
}

interface ReservationConfirmModalProps {
  visible: boolean;
  reservationData: ReservationData | null;
  onConfirm: () => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export const ReservationConfirmModal: React.FC<
  ReservationConfirmModalProps
> = ({ visible, reservationData, onConfirm, onCancel, isLoading = false }) => {
  if (!reservationData) return null;

  const formatDateTime = (dateTime: string) => {
    return DateTime.fromISO(dateTime).toFormat("HH:mm");
  };

  const formatDate = (date: string) => {
    return DateTime.fromISO(date).toFormat("dd/MM/yyyy");
  };

  return (
    <Modal
      isVisible={visible}
      onBackdropPress={!isLoading ? onCancel : undefined}
      onBackButtonPress={!isLoading ? onCancel : undefined}
      style={styles.modal}
      animationIn="slideInUp"
      animationOut="slideOutDown"
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Confirmar Reservación</Text>
        </View>

        <View style={styles.content}>
          <DropDown />

          <View style={styles.infoRow}>
            <MaterialCommunityIcons
              name="spa-outline"
              size={theme.fontSizes.md}
              color={theme.colors.gray700}
            />
            <Text style={styles.label}>Amenidad:</Text>
            <Text style={styles.value}>{reservationData.facilityName}</Text>
          </View>

          <View style={styles.infoRow}>
            <Ionicons
              name="calendar-outline"
              size={theme.fontSizes.md}
              color={theme.colors.gray700}
            />
            <Text style={styles.label}>Fecha:</Text>
            <Text style={styles.value}>
              {formatDate(reservationData.selectedDate)}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Ionicons
              name="time-outline"
              size={theme.fontSizes.md}
              color={theme.colors.gray700}
            />
            <Text style={styles.label}>Horario:</Text>
            <Text style={styles.value}>
              {formatDateTime(reservationData.startDateTime)} -{" "}
              {formatDateTime(reservationData.endDateTime)}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Ionicons
              name="people-outline"
              size={theme.fontSizes.md}
              color={theme.colors.gray700}
            />
            <Text style={styles.label}>Personas:</Text>
            <Text style={styles.value}>{reservationData.amountOfPeople}</Text>
          </View>
        </View>

        <Row>
          <Col gap={9}>
            <Button
              title={isLoading ? "Procesando ..." : "Confirmar reservación"}
              disabled={isLoading}
              onPress={onConfirm}
            />
            <Button
              title="Cancelar"
              onPress={onCancel}
              disabled={isLoading}
              type="Secondary"
            />
          </Col>
        </Row>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: "center",
    margin: theme.spacing.lg,
  },
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.radii.xl,
    padding: theme.spacing.lg,
  },
  header: {
    alignItems: "center",
    marginBottom: theme.spacing.lg,
  },
  title: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "800",
    color: theme.colors.primary,
    marginTop: theme.spacing.sm,
    textAlign: "center",
  },
  content: {
    marginBottom: theme.spacing.lg,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
  },
  label: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.gray700,
    marginLeft: theme.spacing.sm,
    minWidth: 80,
  },
  value: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray900,
    marginLeft: theme.spacing.sm,
    flex: 1,
  },
});
