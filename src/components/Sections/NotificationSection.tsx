import { Section } from "../main";
import { SwitchComponent } from "../SwitchComponent";

interface NotificationSectionProps {
  isEnabled: boolean;
  onToggle: () => void;
  title: string;
  label: string;
}

export const NotificationSection: React.FC<NotificationSectionProps> = ({
  isEnabled,
  onToggle,
  title,
  label,
}) => {
  return (
    <Section title={title}>
      <SwitchComponent
        isEnabled={isEnabled}
        onToggle={() => onToggle()}
        label={`${isEnabled ? "Desactivar" : "Activar"} ${label}`}
      />
    </Section>
  );
};
