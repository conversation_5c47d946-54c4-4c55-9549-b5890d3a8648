import { Switch, Text } from "react-native";
import { theme } from "../theme";
import { Row } from "./main";

interface SwitchProps {
  isEnabled: boolean;
  onToggle: () => void;
  label: string;
}

export const SwitchComponent: React.FC<SwitchProps> = ({
  isEnabled,
  onToggle,
  label,
}) => {
  return (
    <Row justify="space-between" align="center">
      <Text>{label}</Text>
      <Switch
        style={{ transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }] }}
        trackColor={{
          false: theme.colors.gray200,
          true: theme.colors.primaryLight,
        }}
        thumbColor={true ? theme.colors.primary : "#f4f3f4"}
        value={isEnabled}
        onValueChange={onToggle}
      />
    </Row>
  );
};
