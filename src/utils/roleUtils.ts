import { AuthResponse } from "../types/auth";
import { USER_ROLES } from "../navigation/constants";

/**
 * Determina el rol principal del usuario basado en sus roles
 * Prioriza roles administrativos sobre roles de residente
 */
export const getUserPrimaryRole = (user: AuthResponse | null): string => {
  if (!user || !user.roles || user.roles.length === 0) {
    return USER_ROLES.TENANT; // Default role
  }

  const roles = user.roles.map((role) => role.toLowerCase());

  // Prioridad de roles (de mayor a menor)
  if (
    roles.some(
      (role) => role.includes("admin") || role.includes("administrador")
    )
  ) {
    return USER_ROLES.ADMIN;
  }

  if (
    roles.some(
      (role) =>
        role.includes("propertymanager") ||
        role.includes("property_manager") ||
        role.includes("manager") ||
        role.includes("administrador_propiedad")
    )
  ) {
    return USER_ROLES.PROPERTY_MANAGER;
  }

  if (
    roles.some(
      (role) =>
        role.includes("securityguard") ||
        role.includes("security_guard") ||
        role.includes("security") ||
        role.includes("guardia") ||
        role.includes("seguridad")
    )
  ) {
    return USER_ROLES.SECURITY_GUARD;
  }

  if (
    roles.some((role) => role.includes("owner") || role.includes("propietario"))
  ) {
    return USER_ROLES.OWNER;
  }

  // Default to tenant if no specific role is found
  return USER_ROLES.TENANT;
};

/**
 * Verifica si el usuario tiene un rol específico
 */
export const hasRole = (
  user: AuthResponse | null,
  targetRole: string
): boolean => {
  if (!user || !user.roles) return false;

  const userRoles = user.roles.map((role) => role.toLowerCase());
  const target = targetRole.toLowerCase();

  return userRoles.some((role) => role.includes(target));
};

/**
 * Verifica si el usuario es un administrador (Admin o PropertyManager)
 */
export const isAdmin = (user: AuthResponse | null): boolean => {
  const primaryRole = getUserPrimaryRole(user);
  return (
    primaryRole === USER_ROLES.ADMIN ||
    primaryRole === USER_ROLES.PROPERTY_MANAGER
  );
};

/**
 * Verifica si el usuario es staff (SecurityGuard, PropertyManager, o Admin)
 */
export const isStaff = (user: AuthResponse | null): boolean => {
  const primaryRole = getUserPrimaryRole(user);
  return [
    USER_ROLES.SECURITY_GUARD,
    USER_ROLES.PROPERTY_MANAGER,
    USER_ROLES.ADMIN,
  ].includes(primaryRole);
};

/**
 * Verifica si el usuario es residente (Owner o Tenant)
 */
export const isResident = (user: AuthResponse | null): boolean => {
  const primaryRole = getUserPrimaryRole(user);
  return [USER_ROLES.OWNER, USER_ROLES.TENANT].includes(primaryRole);
};

/**
 * Obtiene el nombre del rol para mostrar en la UI
 */
export const getRoleDisplayName = (role: string): string => {
  switch (role) {
    case USER_ROLES.ADMIN:
      return "Administrador";
    case USER_ROLES.PROPERTY_MANAGER:
      return "Administrador de Propiedad";
    case USER_ROLES.SECURITY_GUARD:
      return "Guardia de Seguridad";
    case USER_ROLES.OWNER:
      return "Propietario";
    case USER_ROLES.TENANT:
      return "Inquilino";
    default:
      return "Usuario";
  }
};
