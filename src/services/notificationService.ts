import * as Notifications from "expo-notifications";
import * as Device from "expo-device";
import { Platform } from "react-native";
import { config } from "../config/configuration";

// Configurar el comportamiento de las notificaciones cuando la app está en primer plano
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

export interface NotificationData {
  title: string;
  body: string;
  data?: Record<string, any>;
  sound?: boolean;
  badge?: number;
}

export class NotificationService {
  private static instance: NotificationService;
  private notificationListener: any;
  private responseListener: any;

  private constructor() {
    this.setupNotificationListeners();
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Configura los listeners para manejar notificaciones
   */
  private setupNotificationListeners() {
    // Listener para notificaciones recibidas mientras la app está en primer plano
    this.notificationListener = Notifications.addNotificationReceivedListener(
      (notification) => {
        console.log("Notification received:", notification);
        this.handleNotificationReceived(notification);
      }
    );

    // Listener para cuando el usuario toca una notificación
    this.responseListener =
      Notifications.addNotificationResponseReceivedListener((response) => {
        console.log("Notification response:", response);
        this.handleNotificationResponse(response);
      });
  }

  /**
   * Maneja las notificaciones recibidas cuando la app está en primer plano
   */
  private handleNotificationReceived(notification: Notifications.Notification) {
    // Aquí puedes agregar lógica personalizada para manejar notificaciones
    // Por ejemplo, actualizar el estado de la app, mostrar alertas, etc.
    const { title, body, data } = notification.request.content;
    console.log(`Received notification: ${title} - ${body}`, data);
  }

  /**
   * Maneja las respuestas a notificaciones (cuando el usuario las toca)
   */
  private handleNotificationResponse(
    response: Notifications.NotificationResponse
  ) {
    const { notification } = response;
    const { data } = notification.request.content;

    // Aquí puedes agregar navegación basada en el tipo de notificación
    if (data?.screen && typeof data.screen === "string") {
      console.log(`Navigate to screen: ${data.screen}`);
      // Implementar navegación aquí
    }
  }

  /**
   * Solicita permisos para notificaciones push
   */
  async requestPermissions(): Promise<boolean> {
    if (!Device.isDevice) {
      console.log("Must use physical device for Push Notifications");
      return false;
    }

    const { status: existingStatus } =
      await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== "granted") {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== "granted") {
      console.log("Failed to get push token for push notification!");
      return false;
    }

    return true;
  }

  /**
   * Obtiene el token de push de Expo
   */
  async getExpoPushToken(): Promise<string | null> {
    try {
      const hasPermissions = await this.requestPermissions();
      if (!hasPermissions) {
        return null;
      }

      const token = await Notifications.getExpoPushTokenAsync({
        projectId: config.easProjectId,
      });

      console.log("Expo push token:", token.data);
      return token.data;
    } catch (error) {
      console.error("Error getting push token:", error);
      return null;
    }
  }

  /**
   * Configura el canal de notificaciones para Android
   */
  async setupAndroidChannel() {
    if (Platform.OS === "android") {
      await Notifications.setNotificationChannelAsync("default", {
        name: "default",
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: "#FF231F7C",
        sound: "default",
        enableVibrate: true,
        showBadge: true,
      });
    }
  }

  /**
   * Establece el número de badge de la app
   */
  async setBadgeCount(count: number) {
    await Notifications.setBadgeCountAsync(count);
  }

  /**
   * Obtiene el número actual de badge
   */
  async getBadgeCount(): Promise<number> {
    return await Notifications.getBadgeCountAsync();
  }

  /**
   * Limpia todas las notificaciones de la bandeja de notificaciones
   */
  async clearNotifications() {
    await Notifications.dismissAllNotificationsAsync();
  }

  /**
   * Limpia el badge y las notificaciones
   */
  async clearBadgeAndNotifications() {
    await this.setBadgeCount(0);
    await this.clearNotifications();
  }

  /**
   * Limpia los listeners cuando ya no se necesiten
   */
  cleanup() {
    if (this.notificationListener) {
      this.notificationListener.remove();
    }
    if (this.responseListener) {
      this.responseListener.remove();
    }
  }
}

// Exportar instancia singleton
export const notificationService = NotificationService.getInstance();
