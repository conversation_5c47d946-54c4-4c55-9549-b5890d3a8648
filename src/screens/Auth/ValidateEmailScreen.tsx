import {
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
  StyleSheet,
  KeyboardAvoidingView,
  Text,
  View,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { FormField } from "../../components/forms/FormField";
import { theme } from "../../theme";
import { useZodForm } from "../../hooks/useZodForm";
import {
  ValidateEmailFormValues,
  validateEmailSchema,
} from "../../schemas/schemas";
import { Button, Title } from "../../components";
import { Ionicons } from "@expo/vector-icons";
import { useRegistration } from "../../hooks/useRegistration";
import { RootStackNavigationProp } from "../../navigation";
import { useNavigation } from "@react-navigation/native";

export const ValidateEmailScreen: React.FC = () => {
  const navigation = useNavigation<RootStackNavigationProp>();
  const { validateEmail } = useRegistration();
  const { control, handleSubmit, setError } = useZodForm(validateEmailSchema);

  const onSubmit = async (data: ValidateEmailFormValues) => {
    const response = await validateEmail.mutateAsync(data.email);

    if (response.exists) {
      if (response.confirmed) {
        setError("email", {
          type: "custom",
          message: "El correo ya ha sido confirmado",
        });
        return;
      }
      navigation.navigate("ValidateToken", {
        email: data.email,
      });
    } else {
      setError("email", {
        type: "custom",
        message: "El correo no está registrado. Contacta a administración",
      });
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <LinearGradient
        colors={["#1B4959", "#DFD6C6"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 2.5, y: 0 }}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={styles.area}>
          <KeyboardAvoidingView>
            <View style={styles.container}>
              <Title size="l" style={styles.title}>
                Validación de correo
              </Title>
              <FormField
                control={control}
                name="email"
                placeholder="<EMAIL>"
                keyboardType="email-address"
                icon={
                  <Ionicons
                    name="mail-outline"
                    size={theme.fontSizes.md}
                    color={theme.colors.gray700}
                  />
                }
              />
              <Text style={styles.text}>
                Ingresa el correo registrado con administración para su
                validación.
              </Text>
              <Button onPress={handleSubmit(onSubmit)} title="Validar Correo" />
            </View>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </LinearGradient>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  area: {
    flex: 1,
    justifyContent: "center",
  },
  container: {
    flexGrow: 1,
    paddingLeft: 20,
    paddingRight: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    textAlign: "center",
    color: theme.colors.white,
    marginBottom: theme.spacing.lg,
  },
  text: {
    color: theme.colors.white,
    fontSize: theme.fontSizes.xs,
    marginBottom: theme.spacing.lg,
    textAlign: "center",
  },
});
