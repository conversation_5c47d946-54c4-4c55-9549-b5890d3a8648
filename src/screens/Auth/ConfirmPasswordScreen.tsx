import {
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
  StyleSheet,
  KeyboardAvoidingView,
  View,
  Text,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { FormField } from "../../components/forms/FormField";
import { theme } from "../../theme";
import { useZodForm } from "../../hooks/useZodForm";
import {
  ConfirmPasswordFormValues,
  confirmPasswordSchema,
} from "../../schemas/schemas";
import { Button, Title } from "../../components";
import { Feather } from "@expo/vector-icons";
import { useNavigation, useRoute } from "@react-navigation/native";
import {
  ConfirmPasswordRouteProp,
  RootStackNavigationProp,
} from "../../navigation";
import { useRegistration } from "../../hooks/useRegistration";

export const ConfirmPasswordScreen: React.FC = () => {
  const navigation = useNavigation<RootStackNavigationProp>();
  const route = useRoute<ConfirmPasswordRouteProp>();
  const { token, email } = route.params;
  const { confirmPassword } = useRegistration();

  const { control, handleSubmit } = useZodForm(confirmPasswordSchema);

  const onSubmit = async (data: ConfirmPasswordFormValues) => {
    await confirmPassword.mutateAsync(
      {
        token: token,
        email: email,
        password: data.password,
        passwordConfirmation: data.passwordConfirmation,
      },
      {
        onSuccess: () => {
          navigation.navigate("Auth");
        },
        onError: (error: any) => {
          console.error("Error confirming password:", error.response?.data);
        },
      }
    );
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <LinearGradient
        colors={["#1B4959", "#DFD6C6"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 2.5, y: 0 }}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={styles.area}>
          <KeyboardAvoidingView>
            <View style={styles.container}>
              <Title size="l" style={styles.title}>
                Confirmación de contraseña
              </Title>
              <FormField
                control={control}
                name="password"
                placeholder="••••••••"
                secureTextEntry
                icon={
                  <Feather
                    name="lock"
                    size={theme.fontSizes.md}
                    color={theme.colors.gray700}
                  />
                }
              />
              <FormField
                control={control}
                name="passwordConfirmation"
                placeholder="••••••••"
                secureTextEntry
                icon={
                  <Feather
                    name="lock"
                    size={theme.fontSizes.md}
                    color={theme.colors.gray700}
                  />
                }
              />
              <Text style={styles.text}>
                Ingresa tu nueva contraseña y confírmala.
              </Text>
              <Button
                onPress={handleSubmit(onSubmit)}
                title="Confirmar Contraseña"
              />
            </View>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </LinearGradient>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  area: {
    flex: 1,
    justifyContent: "center",
  },
  container: {
    flexGrow: 1,
    paddingLeft: 20,
    paddingRight: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    textAlign: "center",
    color: theme.colors.white,
    marginBottom: theme.spacing.lg,
  },
  text: {
    color: theme.colors.white,
    fontSize: theme.fontSizes.xs,
    marginBottom: theme.spacing.lg,
    textAlign: "center",
  },
});
