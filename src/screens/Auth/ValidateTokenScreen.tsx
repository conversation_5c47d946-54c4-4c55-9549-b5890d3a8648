import {
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
  StyleSheet,
  KeyboardAvoidingView,
  Text,
  View,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { FormField } from "../../components/forms/FormField";
import { theme } from "../../theme";
import { useZodForm } from "../../hooks/useZodForm";
import { Button, Title } from "../../components";
import { Ionicons } from "@expo/vector-icons";
import { useRegistration } from "../../hooks/useRegistration";
import {
  RootStackNavigationProp,
  ValidateTokenRouteProp,
} from "../../navigation";
import { useNavigation, useRoute } from "@react-navigation/native";
import {
  ValidateTokenFormValues,
  validateTokenSchema,
} from "../../schemas/schemas";

export const ValidateTokenScreen: React.FC = () => {
  const route = useRoute<ValidateTokenRouteProp>();
  const { email } = route.params;
  const navigation = useNavigation<RootStackNavigationProp>();
  const { validateToken } = useRegistration();
  const { control, handleSubmit, setError } = useZodForm(validateTokenSchema);

  const onSubmit = async (data: ValidateTokenFormValues) => {
    const response = await validateToken.mutateAsync({
      token: data.token,
      email: email,
    });

    if (response.valid) {
      navigation.navigate("ConfirmPassword", {
        token: data.token,
        email: email,
      });
      return;
    }
    setError("token", {
      type: "custom",
      message: "Token inválido",
    });
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <LinearGradient
        colors={["#1B4959", "#DFD6C6"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 2.5, y: 0 }}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={styles.area}>
          <KeyboardAvoidingView>
            <View style={styles.container}>
              <Title size="l" style={styles.title}>
                Validación de token
              </Title>
              <FormField
                control={control}
                name="token"
                placeholder="Token"
                keyboardType="numeric"
                icon={
                  <Ionicons
                    name="key-outline"
                    size={theme.fontSizes.md}
                    color={theme.colors.gray700}
                  />
                }
              />
              <Text style={styles.text}>
                Ingresa el token que te enviamos a tu correo.
              </Text>
              <Button onPress={handleSubmit(onSubmit)} title="Validar Token" />
            </View>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </LinearGradient>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  area: {
    flex: 1,
    justifyContent: "center",
  },
  container: {
    flexGrow: 1,
    paddingLeft: 20,
    paddingRight: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    textAlign: "center",
    color: theme.colors.white,
    marginBottom: theme.spacing.lg,
  },
  text: {
    color: theme.colors.white,
    fontSize: theme.fontSizes.xs,
    marginBottom: theme.spacing.lg,
    textAlign: "center",
  },
});
