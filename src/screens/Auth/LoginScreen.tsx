import {
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import sabino from "../../assets/sabino.jpeg";
import { LoginForm } from "../../components/forms/LoginForm";
import { Col } from "../../components/main/Col";
import { Row } from "../../components";
import { useNavigation } from "@react-navigation/native";
import { RootStackNavigationProp } from "../../navigation";

export const LoginScreen: React.FC = () => {
  const navigation = useNavigation<RootStackNavigationProp>();

  const handleRegister = () => {
    navigation.navigate("ValidateEmail");
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <LinearGradient
        colors={["#1B4959", "#DFD6C6"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 2.5, y: 0 }}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={styles.area}>
          <Col style={styles.container}>
            <Image source={sabino} resizeMode="contain" style={styles.logo} />
            <LoginForm />
            <Row align="center" gap={10} style={styles.registerRow}>
              <Text style={styles.text}>¿No tienes una cuenta?</Text>
              <TouchableOpacity onPress={() => handleRegister()}>
                <Text style={styles.link}>Regístrate</Text>
              </TouchableOpacity>
            </Row>
          </Col>
        </SafeAreaView>
      </LinearGradient>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  area: {
    flexGrow: 1,
  },
  container: {
    paddingLeft: 20,
    paddingRight: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  logo: {
    maxWidth: "60%",
  },
  registerRow: {
    marginTop: 40,
  },
  text: {
    color: "#fff",
  },
  link: {
    color: "#fff",
    fontWeight: "bold",
  },
});
