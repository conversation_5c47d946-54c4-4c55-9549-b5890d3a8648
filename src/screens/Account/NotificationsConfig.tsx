import { Platform, Text } from "react-native";
import { Loading } from "../../components/Loading";
import { QUERIES } from "../../constants/queries";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me } from "../../interfaces/me";
import { GradientView } from "../../components/layouts/GradientView";
import { NotificationSection } from "../../components/Sections/NotificationSection";
import { useEffect, useState } from "react";
import { useNotifications } from "../../hooks/useNotifications";
import { usePushNotifications } from "../../hooks/usePushNotifications";

export const NotificationsConfig: React.FC = () => {
  const {
    data: userData,
    isLoading,
    error,
    refetch: refetchUserData,
  } = useCachedQuery<Me>(QUERIES.ME);

  const { registerToken, unregisterToken } = usePushNotifications();
  const {
    expoPushToken,
    hasPermissions,
    requestPermissions,
    isTokenLoading,
    tokenError,
  } = useNotifications();

  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initializeNotifications = async () => {
      if (!hasPermissions) {
        await requestPermissions();
      }
      setIsInitialized(true);
    };

    initializeNotifications();
  }, [hasPermissions, requestPermissions]);

  if (isLoading || isTokenLoading || !isInitialized) return <Loading />;
  if (error) return <Text>Error cargando datos del usuario</Text>;
  if (tokenError) return <Text>Error con notificaciones: {tokenError}</Text>;
  if (!userData) return <Text>Sin datos</Text>;

  const { pushTokens } = userData || {};
  const device = Platform.OS === "ios" ? "iOS" : "Android";

  // Verificar si el token actual está registrado
  const tokenEnabled = pushTokens?.find(
    (token) => token.token === expoPushToken
  );

  const handlePushToggle = async () => {
    if (!hasPermissions) {
      const granted = await requestPermissions();
      if (!granted) {
        return;
      }
    }

    if (!expoPushToken) {
      console.log("No push token available");
      return;
    }

    try {
      if (tokenEnabled) {
        // Desregistrar token
        const tokenToDelete = pushTokens?.find(
          (token) => token.token === expoPushToken
        )?.id;
        if (tokenToDelete) {
          await unregisterToken.mutateAsync(tokenToDelete);
          console.log("Token unregistered successfully");
        }
      } else {
        // Registrar token
        await registerToken.mutateAsync({ token: expoPushToken, device });
        console.log("Token registered successfully");
      }

      // Refrescar datos del usuario
      refetchUserData();
    } catch (error) {
      console.error("Error toggling push notifications:", error);
    }
  };

  return (
    <GradientView firstLineText="Configuración de Notificaciones">
      <NotificationSection
        isEnabled={tokenEnabled !== undefined}
        onToggle={handlePushToggle}
        title="Notificaciones push"
        label={`${tokenEnabled ? "Desactivar" : "Activar"} notificaciones push`}
      />
      <NotificationSection
        isEnabled={true}
        onToggle={() => console.log("Email notifications toggle")}
        title="Notificaciones de correo"
        label="Activar notificaciones de correo"
      />
    </GradientView>
  );
};
