import { Text } from "react-native";
import { useState } from "react";
import { <PERSON>radientView } from "../../components/layouts/GradientView";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { ScrollView } from "react-native-gesture-handler";
import { PropertySelector } from "../../components/Property/PropertySelector";
import { ComplaintsSection } from "../../components/Sections/ComplaintsSection";
import { MaintenanceIssueReportsSection } from "../../components/Sections/MaintenanceIssueReportsSection";
import { PackageSection } from "../../components/Sections/PackageSection";

import { Loading } from "../../components/Loading";
import { PropertyInfoSection } from "../../components/Property/PropertyInfoSection";
import { FinesSection } from "../../components/Property/FinesSection";
import { InfractionsSection } from "../../components/Property/InfractionsSection";
import { ReservationsSection } from "../../components/Property/ReservationsSection";
import { PartialProperty } from "../../interfaces/property";
import { NoDataText } from "../../components/NoDataText";

export const PropertiesScreen: React.FC = () => {
  const {
    data: properties,
    isLoading,
    error,
  } = useCachedQuery<PartialProperty[]>(`mobile/property`);
  const [selectedPropertyId, setSelectedPropertyId] = useState<string | null>(
    null
  );

  if (isLoading) return <Loading />;
  if (error) return <Text>Error al cargar los datos</Text>;
  if (!properties) return <Text>Sin datos</Text>;

  // Si no hay propiedades
  if (properties.length === 0) {
    return (
      <GradientView firstLineText="Mi propiedad">
        <ScrollView>
          <NoDataText text="No tienes propiedades registradas" />
        </ScrollView>
      </GradientView>
    );
  }

  const oneProperty = properties.length === 1;

  // Determinar la propiedad seleccionada
  const selectedProperty = oneProperty
    ? properties[0]
    : properties.find((p) => p.id === selectedPropertyId) || properties[0];

  // Si hay múltiples propiedades pero no se ha seleccionado ninguna, usar la primera
  if (properties.length > 1 && !selectedPropertyId) {
    setSelectedPropertyId(properties[0].id);
  }

  const {
    reservations,
    fines,
    infractions,
    complaints,
    packages,
    maintenanceIssueReports,
  } = selectedProperty;

  return (
    <GradientView firstLineText="Mi propiedad">
      {/* Selector de propiedades para usuarios con múltiples propiedades */}
      {!oneProperty && (
        <PropertySelector
          properties={properties}
          selectedPropertyId={selectedPropertyId}
          onPropertySelect={setSelectedPropertyId}
        />
      )}

      {/* Información básica de la propiedad */}
      <PropertyInfoSection property={selectedProperty} />

      {/* Reservaciones */}
      {reservations?.length > 0 && (
        <ReservationsSection reservations={selectedProperty.reservations} />
      )}

      {/* Multas */}
      {fines?.length > 0 && <FinesSection fines={selectedProperty.fines} />}

      {/* Infracciones */}
      {infractions?.length > 0 && (
        <InfractionsSection infractions={selectedProperty.infractions} />
      )}

      {/* Paquetes */}
      {packages?.length > 0 && (
        <PackageSection packages={selectedProperty.packages} />
      )}

      {/* Quejas */}
      {complaints?.length > 0 && (
        <ComplaintsSection complaints={selectedProperty.complaints} />
      )}

      {/* Reportes de mantenimiento */}
      {maintenanceIssueReports?.length > 0 && (
        <MaintenanceIssueReportsSection
          maintenanceIssueReports={selectedProperty.maintenanceIssueReports}
        />
      )}
    </GradientView>
  );
};
