import React from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity } from "react-native";
import { GradientView } from "../../components/layouts/GradientView";
import { Row } from "../../components/main/Row";
import { Col } from "../../components/main/Col";
import { theme } from "../../theme";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { VISITS_SCREENS } from "../../navigation/constants";

// Mock data - replace with real data from API
const mockVisits = [
  {
    id: "1",
    visitorName: "<PERSON>",
    propertyAddress: "Apto 101",
    status: "active",
    checkInTime: "14:30",
    expectedDuration: "2 horas",
    purpose: "Visita familiar",
  },
  {
    id: "2",
    visitorName: "María García",
    propertyAddress: "Casa 205",
    status: "pending",
    checkInTime: null,
    expectedDuration: "1 hora",
    purpose: "Entrega de documentos",
  },
  {
    id: "3",
    visitorName: "<PERSON>",
    propertyAddress: "Apto 304",
    status: "completed",
    checkInTime: "10:15",
    expectedDuration: "3 horas",
    purpose: "Mantenimiento",
  },
];

export const VisitsListScreen: React.FC = () => {
  const navigation = useNavigation<any>();

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return theme.colors.success;
      case "pending":
        return theme.colors.warning;
      case "completed":
        return theme.colors.gray500;
      default:
        return theme.colors.gray500;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Activa";
      case "pending":
        return "Pendiente";
      case "completed":
        return "Completada";
      default:
        return "Desconocido";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return "account-check";
      case "pending":
        return "account-clock";
      case "completed":
        return "account-check-outline";
      default:
        return "account";
    }
  };

  return (
    <GradientView firstLineText="Visitas" secondLineText="Gestión">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Botón para crear nueva visita */}
        <TouchableOpacity
          style={styles.createButton}
          onPress={() => navigation.navigate(VISITS_SCREENS.CREATE_VISIT)}
        >
          <MaterialCommunityIcons
            name="plus"
            size={24}
            color={theme.colors.white}
          />
          <Text style={styles.createButtonText}>Registrar Nueva Visita</Text>
        </TouchableOpacity>

        {/* Filtros rápidos */}
        <View style={styles.filtersContainer}>
          <Text style={styles.sectionTitle}>Filtros</Text>
          <Row>
            <Col numRows={3}>
              <TouchableOpacity style={[styles.filterButton, styles.activeFilter]}>
                <Text style={styles.filterTextActive}>Todas</Text>
              </TouchableOpacity>
            </Col>
            <Col numRows={3}>
              <TouchableOpacity style={styles.filterButton}>
                <Text style={styles.filterText}>Activas</Text>
              </TouchableOpacity>
            </Col>
            <Col numRows={3}>
              <TouchableOpacity style={styles.filterButton}>
                <Text style={styles.filterText}>Pendientes</Text>
              </TouchableOpacity>
            </Col>
          </Row>
        </View>

        {/* Lista de visitas */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Visitas de Hoy</Text>
          {mockVisits.map((visit) => (
            <TouchableOpacity
              key={visit.id}
              style={styles.visitCard}
              onPress={() => navigation.navigate(VISITS_SCREENS.VISIT_DETAIL, { visitId: visit.id })}
            >
              <Row>
                <Col numRows={1}>
                  <View style={styles.statusIndicator}>
                    <MaterialCommunityIcons
                      name={getStatusIcon(visit.status) as any}
                      size={24}
                      color={getStatusColor(visit.status)}
                    />
                  </View>
                </Col>
                <Col numRows={4}>
                  <View style={styles.visitInfo}>
                    <Text style={styles.visitorName}>{visit.visitorName}</Text>
                    <Text style={styles.propertyAddress}>{visit.propertyAddress}</Text>
                    <Text style={styles.purpose}>{visit.purpose}</Text>
                    {visit.checkInTime && (
                      <Text style={styles.checkInTime}>
                        Check-in: {visit.checkInTime}
                      </Text>
                    )}
                  </View>
                </Col>
                <Col numRows={1}>
                  <View style={styles.statusBadge}>
                    <Text style={[styles.statusText, { color: getStatusColor(visit.status) }]}>
                      {getStatusText(visit.status)}
                    </Text>
                  </View>
                </Col>
              </Row>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  createButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 24,
  },
  createButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  filtersContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: theme.colors.text,
    marginBottom: 12,
  },
  filterButton: {
    backgroundColor: theme.colors.white,
    borderRadius: 8,
    padding: 8,
    marginRight: 8,
    borderWidth: 1,
    borderColor: theme.colors.gray300,
  },
  activeFilter: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  filterText: {
    fontSize: 12,
    color: theme.colors.text,
    textAlign: "center",
  },
  filterTextActive: {
    fontSize: 12,
    color: theme.colors.white,
    textAlign: "center",
  },
  section: {
    marginBottom: 24,
  },
  visitCard: {
    backgroundColor: theme.colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statusIndicator: {
    alignItems: "center",
    justifyContent: "center",
  },
  visitInfo: {
    flex: 1,
    paddingLeft: 12,
  },
  visitorName: {
    fontSize: 16,
    fontWeight: "bold",
    color: theme.colors.text,
    marginBottom: 4,
  },
  propertyAddress: {
    fontSize: 14,
    color: theme.colors.gray600,
    marginBottom: 4,
  },
  purpose: {
    fontSize: 12,
    color: theme.colors.gray500,
    marginBottom: 4,
  },
  checkInTime: {
    fontSize: 12,
    color: theme.colors.primary,
    fontWeight: "500",
  },
  statusBadge: {
    alignItems: "center",
    justifyContent: "center",
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
  },
});
