import React from "react";
import { StyleSheet, Text, View, ScrollView } from "react-native";
import { GradientView } from "../../components/layouts/GradientView";
import { theme } from "../../theme";

export const SecurityPackagesListScreen: React.FC = () => {
  return (
    <GradientView firstLineText="Paquetes" secondLineText="Gestión">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.placeholderContainer}>
          <Text style={styles.placeholderText}>
            Lista de Paquetes
          </Text>
          <Text style={styles.placeholderSubtext}>
            Aquí se mostrará la lista de paquetes para gestionar
          </Text>
        </View>
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 100,
  },
  placeholderText: {
    fontSize: 18,
    fontWeight: "bold",
    color: theme.colors.text,
    textAlign: "center",
    marginBottom: 8,
  },
  placeholderSubtext: {
    fontSize: 14,
    color: theme.colors.gray600,
    textAlign: "center",
  },
});
