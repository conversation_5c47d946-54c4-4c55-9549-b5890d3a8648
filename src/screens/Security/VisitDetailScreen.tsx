import React from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity } from "react-native";
import { GradientView } from "../../components/layouts/GradientView";
import { Row } from "../../components/main/Row";
import { Col } from "../../components/main/Col";
import { theme } from "../../theme";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useRoute, useNavigation } from "@react-navigation/native";
import { VisitsStackParamList } from "../../navigation/types";
import { RouteProp } from "@react-navigation/native";

type VisitDetailRouteProp = RouteProp<VisitsStackParamList, "VisitDetail">;

export const VisitDetailScreen: React.FC = () => {
  const route = useRoute<VisitDetailRouteProp>();
  const navigation = useNavigation<any>();
  const { visitId } = route.params;

  // Mock data - replace with real API call
  const visitData = {
    id: visitId,
    visitorName: "<PERSON>",
    visitorPhone: "+52 ************",
    propertyAddress: "Apto 101",
    residentName: "<PERSON>",
    status: "active",
    checkInTime: "14:30",
    expectedDuration: "2 horas",
    purpose: "Visita familiar",
    notes: "Visitante frecuente, autorizado por el residente",
    vehiclePlate: "ABC-123",
    parkingSpot: "V-15",
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return theme.colors.success;
      case "pending":
        return theme.colors.warning;
      case "completed":
        return theme.colors.gray500;
      default:
        return theme.colors.gray500;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Visita Activa";
      case "pending":
        return "Pendiente de Check-in";
      case "completed":
        return "Visita Completada";
      default:
        return "Estado Desconocido";
    }
  };

  return (
    <GradientView firstLineText="Detalle" secondLineText="de Visita">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Estado de la visita */}
        <View style={[styles.statusCard, { borderLeftColor: getStatusColor(visitData.status) }]}>
          <Row>
            <Col numRows={1}>
              <MaterialCommunityIcons
                name="account-check"
                size={32}
                color={getStatusColor(visitData.status)}
              />
            </Col>
            <Col numRows={4}>
              <Text style={styles.statusTitle}>{getStatusText(visitData.status)}</Text>
              <Text style={styles.statusSubtitle}>
                Check-in: {visitData.checkInTime || "Pendiente"}
              </Text>
            </Col>
          </Row>
        </View>

        {/* Información del visitante */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Información del Visitante</Text>
          <View style={styles.infoCard}>
            <View style={styles.infoRow}>
              <MaterialCommunityIcons name="account" size={20} color={theme.colors.primary} />
              <Text style={styles.infoLabel}>Nombre:</Text>
              <Text style={styles.infoValue}>{visitData.visitorName}</Text>
            </View>
            <View style={styles.infoRow}>
              <MaterialCommunityIcons name="phone" size={20} color={theme.colors.primary} />
              <Text style={styles.infoLabel}>Teléfono:</Text>
              <Text style={styles.infoValue}>{visitData.visitorPhone}</Text>
            </View>
            <View style={styles.infoRow}>
              <MaterialCommunityIcons name="clipboard-text" size={20} color={theme.colors.primary} />
              <Text style={styles.infoLabel}>Propósito:</Text>
              <Text style={styles.infoValue}>{visitData.purpose}</Text>
            </View>
          </View>
        </View>

        {/* Información de la propiedad */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Información de la Propiedad</Text>
          <View style={styles.infoCard}>
            <View style={styles.infoRow}>
              <MaterialCommunityIcons name="home" size={20} color={theme.colors.secondary} />
              <Text style={styles.infoLabel}>Dirección:</Text>
              <Text style={styles.infoValue}>{visitData.propertyAddress}</Text>
            </View>
            <View style={styles.infoRow}>
              <MaterialCommunityIcons name="account-home" size={20} color={theme.colors.secondary} />
              <Text style={styles.infoLabel}>Residente:</Text>
              <Text style={styles.infoValue}>{visitData.residentName}</Text>
            </View>
          </View>
        </View>

        {/* Información del vehículo */}
        {visitData.vehiclePlate && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Información del Vehículo</Text>
            <View style={styles.infoCard}>
              <View style={styles.infoRow}>
                <MaterialCommunityIcons name="car" size={20} color={theme.colors.warning} />
                <Text style={styles.infoLabel}>Placa:</Text>
                <Text style={styles.infoValue}>{visitData.vehiclePlate}</Text>
              </View>
              <View style={styles.infoRow}>
                <MaterialCommunityIcons name="parking" size={20} color={theme.colors.warning} />
                <Text style={styles.infoLabel}>Estacionamiento:</Text>
                <Text style={styles.infoValue}>{visitData.parkingSpot}</Text>
              </View>
            </View>
          </View>
        )}

        {/* Notas */}
        {visitData.notes && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Notas</Text>
            <View style={styles.notesCard}>
              <Text style={styles.notesText}>{visitData.notes}</Text>
            </View>
          </View>
        )}

        {/* Acciones */}
        <View style={styles.actionsSection}>
          {visitData.status === "pending" && (
            <TouchableOpacity style={[styles.actionButton, styles.checkInButton]}>
              <MaterialCommunityIcons name="login" size={20} color={theme.colors.white} />
              <Text style={styles.actionButtonText}>Hacer Check-in</Text>
            </TouchableOpacity>
          )}
          
          {visitData.status === "active" && (
            <TouchableOpacity style={[styles.actionButton, styles.checkOutButton]}>
              <MaterialCommunityIcons name="logout" size={20} color={theme.colors.white} />
              <Text style={styles.actionButtonText}>Hacer Check-out</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity style={[styles.actionButton, styles.editButton]}>
            <MaterialCommunityIcons name="pencil" size={20} color={theme.colors.white} />
            <Text style={styles.actionButtonText}>Editar Visita</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  statusCard: {
    backgroundColor: theme.colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderLeftWidth: 4,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: theme.colors.text,
    marginBottom: 4,
  },
  statusSubtitle: {
    fontSize: 14,
    color: theme.colors.gray600,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: theme.colors.text,
    marginBottom: 12,
  },
  infoCard: {
    backgroundColor: theme.colors.white,
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: theme.colors.gray600,
    marginLeft: 8,
    minWidth: 80,
  },
  infoValue: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: "500",
    flex: 1,
  },
  notesCard: {
    backgroundColor: theme.colors.gray100,
    borderRadius: 8,
    padding: 12,
  },
  notesText: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
  },
  actionsSection: {
    marginTop: 24,
    paddingBottom: 100,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  checkInButton: {
    backgroundColor: theme.colors.success,
  },
  checkOutButton: {
    backgroundColor: theme.colors.warning,
  },
  editButton: {
    backgroundColor: theme.colors.primary,
  },
  actionButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
});
