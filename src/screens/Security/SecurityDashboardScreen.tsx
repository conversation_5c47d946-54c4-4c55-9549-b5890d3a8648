import React from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity } from "react-native";
import { GradientView } from "../../components/layouts/GradientView";
import { Row } from "../../components/main/Row";
import { Col } from "../../components/main/Col";
import { theme } from "../../theme";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { SECURITY_TAB_NAMES, VISITS_SCREENS, SECURITY_PACKAGES_SCREENS, INCIDENTS_SCREENS } from "../../navigation/constants";

export const SecurityDashboardScreen: React.FC = () => {
  const navigation = useNavigation<any>();

  const dashboardCards = [
    {
      title: "Visitas Activas",
      count: "12",
      icon: "account-group",
      color: theme.colors.primary,
      onPress: () => navigation.navigate(SECURITY_TAB_NAMES.VISITS),
    },
    {
      title: "Paquetes Pendientes",
      count: "8",
      icon: "package-variant",
      color: theme.colors.secondary,
      onPress: () => navigation.navigate(SECURITY_TAB_NAMES.PACKAGES),
    },
    {
      title: "Incidentes Hoy",
      count: "3",
      icon: "alert-circle",
      color: theme.colors.warning,
      onPress: () => navigation.navigate(SECURITY_TAB_NAMES.INCIDENTS),
    },
    {
      title: "Check-ins Hoy",
      count: "45",
      icon: "login",
      color: theme.colors.success,
      onPress: () => navigation.navigate(SECURITY_TAB_NAMES.VISITS, {
        screen: VISITS_SCREENS.VISITS_LIST,
      }),
    },
  ];

  const quickActions = [
    {
      title: "Registrar Visita",
      icon: "account-plus",
      color: theme.colors.primary,
      onPress: () => navigation.navigate(SECURITY_TAB_NAMES.VISITS, {
        screen: VISITS_SCREENS.CREATE_VISIT,
      }),
    },
    {
      title: "Recibir Paquete",
      icon: "package-down",
      color: theme.colors.secondary,
      onPress: () => navigation.navigate(SECURITY_TAB_NAMES.PACKAGES, {
        screen: SECURITY_PACKAGES_SCREENS.RECEIVE_PACKAGE,
      }),
    },
    {
      title: "Reportar Incidente",
      icon: "alert-plus",
      color: theme.colors.warning,
      onPress: () => navigation.navigate(SECURITY_TAB_NAMES.INCIDENTS, {
        screen: INCIDENTS_SCREENS.CREATE_INCIDENT,
      }),
    },
  ];

  return (
    <GradientView firstLineText="Dashboard" secondLineText="Seguridad">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Resumen de estadísticas */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Resumen del Día</Text>
          <Row>
            {dashboardCards.map((card, index) => (
              <Col key={index} numRows={2}>
                <TouchableOpacity
                  style={[styles.card, { borderLeftColor: card.color }]}
                  onPress={card.onPress}
                >
                  <View style={styles.cardHeader}>
                    <MaterialCommunityIcons
                      name={card.icon as any}
                      size={24}
                      color={card.color}
                    />
                    <Text style={styles.cardCount}>{card.count}</Text>
                  </View>
                  <Text style={styles.cardTitle}>{card.title}</Text>
                </TouchableOpacity>
              </Col>
            ))}
          </Row>
        </View>

        {/* Acciones rápidas */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Acciones Rápidas</Text>
          <Row>
            {quickActions.map((action, index) => (
              <Col key={index} numRows={3}>
                <TouchableOpacity
                  style={[styles.actionCard, { backgroundColor: action.color }]}
                  onPress={action.onPress}
                >
                  <MaterialCommunityIcons
                    name={action.icon as any}
                    size={32}
                    color={theme.colors.white}
                  />
                  <Text style={styles.actionTitle}>{action.title}</Text>
                </TouchableOpacity>
              </Col>
            ))}
          </Row>
        </View>

        {/* Información adicional */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Estado del Sistema</Text>
          <View style={styles.statusCard}>
            <Row>
              <Col numRows={2}>
                <View style={styles.statusItem}>
                  <MaterialCommunityIcons
                    name="shield-check"
                    size={20}
                    color={theme.colors.success}
                  />
                  <Text style={styles.statusText}>Sistema Activo</Text>
                </View>
              </Col>
              <Col numRows={2}>
                <View style={styles.statusItem}>
                  <MaterialCommunityIcons
                    name="wifi"
                    size={20}
                    color={theme.colors.success}
                  />
                  <Text style={styles.statusText}>Conectado</Text>
                </View>
              </Col>
            </Row>
          </View>
        </View>
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: theme.colors.text,
    marginBottom: 12,
  },
  card: {
    backgroundColor: theme.colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    borderLeftWidth: 4,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  cardCount: {
    fontSize: 24,
    fontWeight: "bold",
    color: theme.colors.text,
  },
  cardTitle: {
    fontSize: 14,
    color: theme.colors.gray600,
  },
  actionCard: {
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
    minHeight: 100,
  },
  actionTitle: {
    fontSize: 12,
    fontWeight: "600",
    color: theme.colors.white,
    textAlign: "center",
    marginTop: 8,
  },
  statusCard: {
    backgroundColor: theme.colors.white,
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statusItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  statusText: {
    fontSize: 14,
    color: theme.colors.text,
    marginLeft: 8,
  },
});
