import { Protocol } from "./protocol";
import { Regulation } from "./regulation";
import { Reservation } from "./reservation";

export interface Facility {
  id: string;
  name: string;
  description: string;
  open: string;
  close: string;
  imagePath: string;
  reservable: boolean;
  daysOfWeek: number[];
  startTime?: string;
  endTime?: string;
  maxAmountOfPeople?: number;
  maxTimeOfStay?: number;
  createdAt: string;
  updatedAt: string;
  reservations: Reservation[];
  regulations: Regulation[];
  protocols: Protocol[];
}

export interface PartialFacility {
  id: string;
  name: string;
  open: string;
  close: string;
  imagePath: string;
  reservable: boolean;
  daysOfWeek?: number[];
  startTime?: string;
  endTime?: string;
  maxAmountOfPeople?: number;
  maxTimeOfStay?: number;
}
