export interface ConfirmPasswordRequest extends PasswordConfirmationValues {
  token: string;
}

export interface ConfirmPasswordResponse {
  message: string;
}

export interface PasswordConfirmationValues {
  password: string;
  passwordConfirmation: string;
}

export interface AuthRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  firstName: string;
  lastName: string;
  roles: string[];
}

export interface ValidateEmailResponse {
  exists: boolean;
  confirmed?: boolean;
}

export interface ValidateTokenResponse {
  valid: boolean;
}

export interface ValidateTokenRequest {
  token: string;
  email: string;
}
