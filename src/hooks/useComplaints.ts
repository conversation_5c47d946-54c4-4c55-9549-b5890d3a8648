import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { hoaClient } from "../api/axios-clients";
import { Complaint, CreateComplaint } from "../interfaces/complaint";
import { ComplaintType } from "../interfaces/complaint-type";

export const useComplaints = () => {
  const queryClient = useQueryClient();

  // Obtener tipos de queja
  const complaintTypes = useQuery({
    queryKey: ["complaint-types"],
    queryFn: async () => {
      const response = await hoaClient.get<ComplaintType[]>(
        "/mobile/complaint-types"
      );
      return response.data;
    },
  });

  // Crear una nueva queja
  const createComplaint = useMutation({
    mutationFn: async (data: CreateComplaint) => {
      const formData = new FormData();
      formData.append("propertyId", data.propertyId);
      formData.append("complaintTypeId", data.complaintTypeId);
      formData.append("detail", data.detail);
      formData.append("priority", data.priority);
      data.images?.forEach((image, index) => {
        formData.append(`files`, {
          uri: image,
          type: "image/jpeg",
          name: `image${index}.jpg`,
        } as any);
      });

      const response = await hoaClient.post<Complaint>(
        "/mobile/complaint",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      return response.data;
    },
    onSuccess: () => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: ["complaint"] });
      queryClient.invalidateQueries({ queryKey: ["me"] });
    },
    onError: (error: any) => {
      console.error(error.response?.data);
    },
  });

  return {
    complaintTypes,
    createComplaint,
  };
};
