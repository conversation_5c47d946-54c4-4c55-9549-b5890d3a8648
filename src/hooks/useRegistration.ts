//Validate email: endponit => validate-email/:email, response => ValidateEmailResponse

import { useMutation } from "@tanstack/react-query";
import { hoaClient } from "../api/axios-clients";
import {
  ValidateEmailResponse,
  ValidateTokenRequest,
  ValidateTokenResponse,
} from "../interfaces/auth";
import { ConfirmPasswordRequest, ConfirmPasswordResponse } from "../types/auth";

export const useRegistration = () => {
  const validateEmail = useMutation({
    mutationFn: async (email: string) => {
      const response = await hoaClient.post<ValidateEmailResponse>(
        `/auth/validate-email`,
        { email: email }
      );
      return response.data;
    },
  });

  const validateToken = useMutation({
    mutationFn: async (validateTokenRequest: ValidateTokenRequest) => {
      console.log(validateTokenRequest);
      const response = await hoaClient.post<ValidateTokenResponse>(
        "/auth/validate-token",
        validateTokenRequest
      );
      console.log(response.data);
      return response.data;
    },
    onError: (error: any) => {
      console.error("Error validating token:", error.response?.data);
    },
  });

  const confirmPassword = useMutation({
    mutationFn: async (confirmPasswordRequest: ConfirmPasswordRequest) => {
      console.log(confirmPasswordRequest);
      await hoaClient.post<ConfirmPasswordResponse>(
        "/auth/confirm-password",
        confirmPasswordRequest
      );
      return true;
    },
  });

  return {
    validateEmail,
    confirmPassword,
    validateToken,
  };
};
