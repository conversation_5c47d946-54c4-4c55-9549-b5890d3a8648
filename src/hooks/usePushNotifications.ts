import { useMutation, useQueryClient } from "@tanstack/react-query";
import { hoaClient } from "../api/axios-clients";
import { CreatePushToken, PushToken } from "../interfaces/push.token";
import { QUERIES } from "../constants/queries";

export const usePushNotifications = () => {
  const queryClient = useQueryClient();

  const registerToken = useMutation({
    mutationFn: async (data: CreatePushToken) => {
      const response = await hoaClient.post(`/mobile/push-token`, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERIES.ME] });
    },
  });

  const unregisterToken = useMutation({
    mutationFn: async (tokenId: PushToken["id"]) => {
      const response = await hoaClient.delete(`/mobile/push-token/${tokenId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERIES.ME] });
    },
  });

  return {
    registerToken,
    unregisterToken,
  };
};
