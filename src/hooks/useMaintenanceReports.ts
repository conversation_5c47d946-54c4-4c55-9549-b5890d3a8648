import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { hoaClient } from "../api/axios-clients";
import {
  CreateMaintenanceIssueReport,
  MaintenanceIssueReport,
} from "../interfaces/maintenance-issue-report";
import { Facility } from "../interfaces/facility";

export const useMaintenanceReports = () => {
  const queryClient = useQueryClient();

  // Obtener amenidades para el selector
  const facilities = useQuery({
    queryKey: ["facilities"],
    queryFn: async () => {
      const response = await hoaClient.get<Facility[]>("/mobile/facilities");
      return response.data;
    },
  });

  // Crear un nuevo reporte de mantenimiento
  const createMaintenanceReport = useMutation({
    mutationFn: async (data: CreateMaintenanceIssueReport) => {
      const formData = new FormData();
      formData.append("propertyId", data.propertyId);
      formData.append("description", data.description);
      data.images?.forEach((image, index) => {
        formData.append(`files`, {
          uri: image,
          type: "image/jpeg",
          name: `image${index}.jpg`,
        } as any);
      });

      const response = await hoaClient.post<MaintenanceIssueReport>(
        "/mobile/maintenance-issue-report",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      return response.data;
    },
    onSuccess: () => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({
        queryKey: ["maintenance-issue-reports"],
      });
      queryClient.invalidateQueries({ queryKey: ["me"] });
    },
    onError: (error: any) => {
      console.error("Error creating maintenance report:", error.response?.data);
    },
  });

  return {
    facilities,
    createMaintenanceReport,
  };
};
