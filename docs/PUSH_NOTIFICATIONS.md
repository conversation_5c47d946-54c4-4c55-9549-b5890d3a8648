# Push Notifications Implementation

Esta documentación explica cómo están implementadas las push notifications en la aplicación ResApp.

## Arquitectura

### Componentes Principales

1. **NotificationService** (`src/services/notificationService.ts`)
   - Ser<PERSON><PERSON> singleton que maneja toda la lógica de notificaciones
   - Configura listeners para notificaciones recibidas y respuestas
   - Maneja permisos, tokens, y notificaciones locales

2. **useNotifications Hook** (`src/hooks/useNotifications.ts`)
   - Hook personalizado que expone la funcionalidad de notificaciones
   - Maneja el estado de tokens, permisos, y notificaciones
   - Integra con el servicio de notificaciones

3. **usePushNotifications Hook** (`src/hooks/usePushNotifications.ts`)
   - Hook para registrar/desregistrar tokens en el backend
   - Maneja las mutaciones de API para tokens de push

4. **NotificationHandler Component** (`src/components/NotificationHandler.tsx`)
   - Componente wrapper que maneja notificaciones globalmente
   - Debe estar en el nivel superior de la app

5. **NotificationsConfig Screen** (`src/screens/Account/NotificationsConfig.tsx`)
   - Pantalla de configuración para que el usuario active/desactive notificaciones

## Configuración

### 1. App Configuration

El archivo `app.config.ts` incluye la configuración del plugin de notificaciones:

```typescript
plugins: [
  [
    "expo-notifications",
    {
      icon: "./src/assets/notification-icon.png",
      color: "#ffffff",
      defaultChannel: "default",
      sounds: ["./src/assets/notification-sound.wav"],
    },
  ],
]
```

### 2. Permisos

Las notificaciones requieren permisos del usuario:
- **iOS**: Se solicitan automáticamente
- **Android**: Se configuran canales de notificación

### 3. Tokens

Los tokens de Expo Push se obtienen automáticamente y se registran en el backend a través de los endpoints:
- `POST /mobile/push-token` - Registrar token
- `DELETE /mobile/push-token/:id` - Desregistrar token

## Uso

### Configuración Básica

```typescript
import { useNotifications } from "../hooks/useNotifications";

const MyComponent = () => {
  const {
    expoPushToken,
    hasPermissions,
    requestPermissions,
    scheduleLocalNotification,
  } = useNotifications();

  // Solicitar permisos
  const handleRequestPermissions = async () => {
    const granted = await requestPermissions();
    if (granted) {
      console.log("Permisos concedidos");
    }
  };

  // Enviar notificación local
  const sendNotification = async () => {
    await scheduleLocalNotification({
      title: "Título",
      body: "Mensaje de la notificación",
      data: { screen: "Home" },
    });
  };
};
```

### Notificaciones Programadas

```typescript
// Notificación en 1 hora
await scheduleLocalNotification(
  {
    title: "Recordatorio",
    body: "No olvides revisar tus mensajes",
  },
  {
    seconds: 3600,
  }
);

// Notificación diaria
await scheduleLocalNotification(
  {
    title: "Notificación diaria",
    body: "Tu resumen diario está listo",
  },
  {
    repeats: true,
    hour: 9,
    minute: 0,
  }
);
```

### Manejo de Respuestas

El `NotificationHandler` maneja automáticamente las respuestas a notificaciones:

```typescript
// En el data de la notificación puedes incluir:
{
  screen: "PropertyDetails", // Pantalla a la que navegar
  params: { propertyId: "123" }, // Parámetros de navegación
  action: "refresh", // Acción específica a realizar
}
```

## Backend Integration

### Endpoints Requeridos

1. **Registrar Token**
   ```
   POST /mobile/push-token
   Body: { token: string, device: string }
   ```

2. **Desregistrar Token**
   ```
   DELETE /mobile/push-token/:id
   ```

3. **Enviar Notificación** (desde el backend)
   ```
   POST https://exp.host/--/api/v2/push/send
   Headers: {
     "Accept": "application/json",
     "Accept-encoding": "gzip, deflate",
     "Content-Type": "application/json"
   }
   Body: {
     "to": "ExponentPushToken[...]",
     "title": "Título",
     "body": "Mensaje",
     "data": { "screen": "Home" }
   }
   ```

## Testing

### Componente de Prueba

Incluye `NotificationTestButtons` en tu pantalla de desarrollo:

```typescript
import { NotificationTestButtons } from "../components/NotificationTestButtons";

// En tu componente de desarrollo
<NotificationTestButtons />
```

### Herramientas de Testing

1. **Expo Push Tool**: https://expo.dev/notifications
2. **Postman/cURL** para probar endpoints del backend
3. **Logs de consola** para debugging

## Troubleshooting

### Problemas Comunes

1. **Token no se genera**
   - Verificar que `easProjectId` esté configurado correctamente
   - Asegurar que la app esté corriendo en un dispositivo físico

2. **Notificaciones no aparecen**
   - Verificar permisos del usuario
   - Revisar configuración del canal de Android
   - Verificar que la app no esté en primer plano (para notificaciones push)

3. **Token no se registra en el backend**
   - Verificar endpoints del backend
   - Revisar logs de red en el cliente

### Logs Útiles

```typescript
// Habilitar logs detallados
console.log("Push token:", expoPushToken);
console.log("Has permissions:", hasPermissions);
console.log("Last notification:", lastNotification);
```

## Consideraciones de Producción

1. **Rate Limiting**: Expo tiene límites en el número de notificaciones por hora
2. **Token Expiration**: Los tokens pueden expirar, implementar renovación automática
3. **Error Handling**: Manejar errores de red y tokens inválidos
4. **Privacy**: Informar a los usuarios sobre el uso de notificaciones
5. **Batching**: Agrupar notificaciones similares para evitar spam

## Assets Requeridos

Crear los siguientes archivos en `src/assets/`:
- `notification-icon.png` (96x96px, transparente)
- `notification-sound.wav` (opcional, sonido personalizado)

## Próximos Pasos

1. Implementar navegación basada en notificaciones
2. Agregar categorías de notificaciones
3. Implementar notificaciones ricas (imágenes, botones)
4. Configurar analytics para tracking de notificaciones
