# 🧪 Testing de Navegación desde Notificaciones

## 📋 Resumen

Guía para probar la funcionalidad de navegación automática desde notificaciones push.

## 🔧 Configuración de Pruebas

### 1. Verificar que el NotificationHandler esté funcionando

Después de los cambios, el `NotificationHandler` ahora está ubicado en `AppNavigator.tsx` dentro del `NavigationContainer`, lo que resuelve el error:

```
Couldn't find a navigation object. Is your component inside NavigationContainer?
```

### 2. Estructura de Prueba

```
App.tsx
└── QueryClientProvider
    └── AuthProvider
        └── AppNavigator.tsx
            └── NavigationContainer
                └── NotificationHandler ✅ (Ahora tiene acceso a navegación)
                    └── MainNavigator | AuthNavigator
```

## 🚀 Pruebas Manuales

### Método 1: Usando Expo Push Tool

1. **Obtener el Push Token**
   - Abrir la app en desarrollo
   - El token se imprime en la consola al iniciar

2. **Enviar Notificación de Prueba**
   - Ir a: https://expo.dev/notifications
   - Pegar el push token
   - Usar los siguientes payloads:

#### Prueba de Multa
```json
{
  "to": "ExponentPushToken[TU_TOKEN_AQUI]",
  "title": "Nueva multa registrada",
  "body": "Se ha registrado una multa de $50",
  "data": {
    "type": "fine",
    "fineId": "fine_test_001"
  }
}
```

#### Prueba de Paquete
```json
{
  "to": "ExponentPushToken[TU_TOKEN_AQUI]",
  "title": "Paquete recibido",
  "body": "Tu paquete de Amazon ha llegado",
  "data": {
    "type": "package",
    "packageId": "pkg_test_001"
  }
}
```

#### Prueba de Infracción
```json
{
  "to": "ExponentPushToken[TU_TOKEN_AQUI]",
  "title": "Nueva infracción",
  "body": "Infracción de estacionamiento registrada",
  "data": {
    "type": "infraction",
    "infractionId": "inf_test_001"
  }
}
```

#### Prueba de Comunicado
```json
{
  "to": "ExponentPushToken[TU_TOKEN_AQUI]",
  "title": "Nuevo comunicado",
  "body": "Mantenimiento programado para el sábado",
  "data": {
    "type": "announcement"
  }
}
```

### Método 2: Usando cURL

```bash
# Multa
curl -H "Content-Type: application/json" \
     -X POST \
     -d '{
       "to": "ExponentPushToken[TU_TOKEN]",
       "title": "Nueva multa",
       "body": "Multa de $50 registrada",
       "data": {
         "type": "fine",
         "fineId": "fine_test_001"
       }
     }' \
     https://exp.host/--/api/v2/push/send

# Paquete
curl -H "Content-Type: application/json" \
     -X POST \
     -d '{
       "to": "ExponentPushToken[TU_TOKEN]",
       "title": "Paquete recibido",
       "body": "Paquete en recepción",
       "data": {
         "type": "package",
         "packageId": "pkg_test_001"
       }
     }' \
     https://exp.host/--/api/v2/push/send
```

## 🔍 Verificación de Funcionamiento

### 1. Logs de Consola

Al tocar una notificación, deberías ver en la consola:

```
Notification tapped: [objeto de notificación]
Navigating to: fine with ID: fine_test_001
```

### 2. Navegación Esperada

| Tipo | Navegación Esperada |
|------|-------------------|
| `fine` | PropertyTab → FineDetail |
| `package` | PropertyTab → PackageDetail |
| `infraction` | PropertyTab → InfractionDetail |
| `announcement` | DashboardTab → Dashboard |

### 3. Manejo de Errores

Si hay problemas, verifica:

- ✅ El `NotificationHandler` está en `AppNavigator.tsx`
- ✅ Las constantes `TAB_NAMES` y `PROPERTY_SCREENS` están importadas
- ✅ Las pantallas de detalle existen en `PropertyStack`
- ✅ Los IDs en los datos de prueba son válidos

## 🐛 Troubleshooting

### Error: "Couldn't find a navigation object"
- **Causa**: `NotificationHandler` fuera del `NavigationContainer`
- **Solución**: ✅ Ya resuelto - movido a `AppNavigator.tsx`

### Error: "Screen not found"
- **Causa**: Nombre de pantalla incorrecto en constantes
- **Solución**: Verificar `PROPERTY_SCREENS` y `TAB_NAMES`

### No navega al tocar notificación
- **Causa**: Datos de notificación incorrectos
- **Solución**: Verificar estructura del campo `data`

### Notificación no aparece
- **Causa**: Token inválido o permisos
- **Solución**: Verificar permisos y regenerar token

## 📱 Pruebas en Dispositivo Real

### iOS
1. Compilar con `expo run:ios`
2. Enviar notificación usando Expo Push Tool
3. Poner app en background
4. Tocar notificación cuando aparezca

### Android
1. Compilar con `expo run:android`
2. Enviar notificación usando Expo Push Tool
3. Poner app en background
4. Tocar notificación en el panel de notificaciones

## ✅ Checklist de Pruebas

- [ ] Notificación de multa navega a FineDetail
- [ ] Notificación de paquete navega a PackageDetail
- [ ] Notificación de infracción navega a InfractionDetail
- [ ] Notificación de comunicado navega a Dashboard
- [ ] Logs aparecen correctamente en consola
- [ ] No hay errores de navegación
- [ ] Funciona en iOS
- [ ] Funciona en Android
- [ ] Funciona con app en background
- [ ] Funciona con app cerrada

## 📝 Notas Importantes

1. **IDs de Prueba**: Los IDs usados en las pruebas (`fine_test_001`, etc.) deben existir en tu backend o las pantallas de detalle mostrarán errores.

2. **Permisos**: Asegúrate de que los permisos de notificaciones estén habilitados.

3. **Desarrollo vs Producción**: En desarrollo, las notificaciones pueden comportarse diferente que en producción.

4. **Timing**: Espera unos segundos después de enviar la notificación antes de tocarla.
