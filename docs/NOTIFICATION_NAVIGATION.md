# 🧭 Navegación Automática desde Notificaciones Push

## 📋 Resumen

El sistema de notificaciones push ahora incluye navegación automática a pantallas específicas basada en el tipo de notificación recibida.

## 🎯 Tipos de Notificación Soportados

### 1. **<PERSON><PERSON><PERSON> (Fines)**
```json
{
  "title": "Nueva multa registrada",
  "body": "Se ha registrado una nueva multa en tu propiedad",
  "data": {
    "type": "fine",
    "fineId": "fine_123456"
  }
}
```
**Navegación**: `PropertyTab → FineDetail`

### 2. **Paquetes (Packages)**
```json
{
  "title": "Nuevo paquete recibido",
  "body": "Tienes un nuevo paquete en recepción",
  "data": {
    "type": "package",
    "packageId": "pkg_789012"
  }
}
```
**Navegación**: `PropertyTab → PackageDetail`

### 3. **Infracciones (Infractions)**
```json
{
  "title": "Nueva infracción registrada",
  "body": "Se ha registrado una infracción en tu propiedad",
  "data": {
    "type": "infraction",
    "infractionId": "inf_345678"
  }
}
```
**Navegación**: `PropertyTab → InfractionDetail`

### 4. **Comunicados (Announcements)**
```json
{
  "title": "Nuevo comunicado",
  "body": "Hay un nuevo comunicado de la administración",
  "data": {
    "type": "announcement"
  }
}
```
**Navegación**: `DashboardTab → Dashboard`

## 🔧 Implementación Técnica

### Estructura del Componente
El `NotificationHandler` maneja automáticamente la navegación:

```typescript
interface NotificationData {
  type: "fine" | "package" | "infraction" | "announcement" | "comunicado";
  fineId?: string;
  packageId?: string;
  infractionId?: string;
  title?: string;
  message?: string;
  action?: string;
}
```

### Flujo de Navegación
1. **Recepción**: La notificación llega al dispositivo
2. **Tap del Usuario**: El usuario toca la notificación
3. **Procesamiento**: El `NotificationHandler` lee el campo `data.type`
4. **Navegación**: Se navega automáticamente a la pantalla correspondiente
5. **Limpieza**: Se limpian las notificaciones y badges

## 📱 Rutas de Navegación

| Tipo | Tab Destino | Pantalla Destino | Parámetros Requeridos |
|------|-------------|------------------|----------------------|
| `fine` | PropertyTab | FineDetail | `fineId` |
| `package` | PropertyTab | PackageDetail | `packageId` |
| `infraction` | PropertyTab | InfractionDetail | `infractionId` |
| `announcement` | DashboardTab | Dashboard | ninguno |
| `comunicado` | DashboardTab | Dashboard | ninguno |

## 🚀 Uso desde el Backend

### Envío de Notificación con Expo Push API

```javascript
const message = {
  to: userPushToken,
  sound: 'default',
  title: 'Nueva multa registrada',
  body: 'Se ha registrado una nueva multa en tu propiedad',
  data: {
    type: 'fine',
    fineId: 'fine_123456'
  },
};

await fetch('https://exp.host/--/api/v2/push/send', {
  method: 'POST',
  headers: {
    Accept: 'application/json',
    'Accept-encoding': 'gzip, deflate',
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(message),
});
```

## ⚠️ Consideraciones Importantes

### 1. **Validación de IDs**
- Asegúrate de que los IDs enviados existan en la base de datos
- Los IDs deben ser válidos y accesibles para el usuario

### 2. **Manejo de Errores**
- Si falta el `type` o los IDs requeridos, la navegación no ocurrirá
- Los errores se registran en la consola para debugging

### 3. **Permisos de Usuario**
- El usuario debe tener acceso a la entidad referenciada
- Verificar permisos antes de enviar la notificación

### 4. **Tipos Alternativos**
- `comunicado` es un alias de `announcement`
- Ambos navegan al Dashboard

## 🔍 Debugging

Para verificar que las notificaciones funcionan correctamente:

```typescript
// En el NotificationHandler se registran logs:
console.log("Notification tapped:", lastNotificationResponse);
console.log("Navigating to:", data.type, "with ID:", data.fineId || data.packageId || data.infractionId);
```

## 📝 Ejemplos de Uso

### Multa Registrada
```json
{
  "title": "Multa por ruido excesivo",
  "body": "Se ha registrado una multa de $50 por ruido excesivo",
  "data": {
    "type": "fine",
    "fineId": "fine_20241207_001"
  }
}
```

### Paquete Recibido
```json
{
  "title": "Paquete de Amazon",
  "body": "Tu paquete de Amazon ha llegado a recepción",
  "data": {
    "type": "package",
    "packageId": "pkg_amazon_20241207"
  }
}
```

### Infracción de Estacionamiento
```json
{
  "title": "Infracción de estacionamiento",
  "body": "Vehículo estacionado en lugar prohibido",
  "data": {
    "type": "infraction",
    "infractionId": "inf_parking_001"
  }
}
```

### Comunicado General
```json
{
  "title": "Mantenimiento programado",
  "body": "Habrá mantenimiento de elevadores el sábado",
  "data": {
    "type": "announcement"
  }
}
```
