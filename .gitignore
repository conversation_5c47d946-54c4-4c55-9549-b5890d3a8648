# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local
.env

# typescript
*.tsbuildinfo

# Builds y salidas temporales
ios/build/
android/app/build/
android/.gradle/
android/.idea/
android/.cxx/
android/local.properties

# Pods de iOS (si usas CocoaPods)
ios/Pods/
ios/Podfile.lock

# Archivos de estado de metro bundler
metro-cache/
react-native-packager-cache-*/

# Archivos de bundling
*.bundle
*.map

# Cache de babel y otros
babel.config.js
.expo/
.expo-shared/
*.log

# Cache de testing
coverage/
jest-cache/
jest-test-results.json

# Carpetas nativas generadas automáticamente
ios/DerivedData/

# Archivos de certificados (si generas firmados)
*.keystore
*.jks
*.p12

# Carpeta temporal para OTA
dist/
tmp/