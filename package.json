{"name": "resapp", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@hookform/resolvers": "^4.1.3", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-picker/picker": "2.11.0", "@react-navigation/bottom-tabs": "^7.3.3", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "@tanstack/react-query": "^5.70.0", "axios": "^1.8.4", "expo": "~53.0.8", "expo-constants": "^17.1.7", "expo-device": "^7.1.4", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.4", "expo-location": "~18.1.4", "expo-notifications": "~0.31.4", "expo-secure-store": "~14.2.3", "expo-status-bar": "~2.2.3", "luxon": "^3.6.0", "react": "19.0.0", "react-hook-form": "^7.55.0", "react-native": "0.79.2", "react-native-calendars": "^1.1311.1", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.24.0", "react-native-modal": "^14.0.0-rc.1", "react-native-modal-datetime-picker": "^18.0.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-webview": "^13.13.5", "react-navigation": "^5.0.0", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/luxon": "^3.4.2", "@types/react": "~19.0.10", "@types/react-navigation": "^3.0.8", "typescript": "^5.3.3"}, "private": true}