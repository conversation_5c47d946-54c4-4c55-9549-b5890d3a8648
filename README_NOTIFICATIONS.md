# 🔔 Push Notifications - ResApp

## ✅ Estado de Implementación

Las push notifications han sido completamente implementadas en la aplicación ResApp para **recibir notificaciones desde el backend**. Aquí está lo que se ha configurado:

### 🎯 Funcionalidades Implementadas

- ✅ **Servicio de Notificaciones** - Manejo completo de notificaciones push
- ✅ **Hook personalizado** - `useNotifications` para fácil integración
- ✅ **Configuración de usuario** - Pantalla para activar/desactivar notificaciones
- ✅ **Registro de tokens** - Integración con backend para gestión de dispositivos
- ✅ **Manejo global** - Component wrapper para manejo de notificaciones en toda la app
- ✅ **Navegación automática** - A pantallas específicas (multas, paquetes, infracciones, comunicados)

### 📁 Archivos Creados/Modificados

```
src/
├── services/
│   └── notificationService.ts          # Servicio principal de notificaciones
├── hooks/
│   ├── useNotifications.ts             # Hook personalizado para notificaciones
│   └── usePushNotifications.ts         # Hook para API de tokens (ya existía)
├── components/
│   └── NotificationHandler.tsx         # Wrapper global para notificaciones
├── screens/Account/
│   └── NotificationsConfig.tsx         # Pantalla de configuración (mejorada)
├── navigation/
│   └── AppNavigator.tsx               # Integración del NotificationHandler
└── App.tsx                            # Punto de entrada principal

app.config.ts                          # Configuración del plugin de notificaciones
docs/PUSH_NOTIFICATIONS.md             # Documentación técnica detallada
```

## 🚀 Cómo Usar

### 1. Configuración Básica

Las notificaciones ya están configuradas globalmente. El sistema está listo para **recibir** notificaciones push desde el backend:

```typescript
import { useNotifications } from "../hooks/useNotifications";

const MyComponent = () => {
  const {
    expoPushToken,
    hasPermissions,
    requestPermissions,
    badgeCount,
    lastNotification,
    lastNotificationResponse,
  } = useNotifications();

  // El token está listo para enviar al backend
  // Las notificaciones se reciben automáticamente
};
```

### 2. Configurar Notificaciones del Usuario

Los usuarios pueden activar/desactivar notificaciones en:
**Cuenta → Notificaciones**

### 3. Navegación Automática

Las notificaciones navegan automáticamente a pantallas específicas según el tipo:

#### **Multas**

```json
{
  "title": "Nueva multa registrada",
  "body": "Se ha registrado una nueva multa",
  "data": {
    "type": "fine",
    "fineId": "fine_123456"
  }
}
```

#### **Paquetes**

```json
{
  "title": "Nuevo paquete recibido",
  "body": "Tienes un paquete en recepción",
  "data": {
    "type": "package",
    "packageId": "pkg_789012"
  }
}
```

#### **Infracciones**

```json
{
  "title": "Nueva infracción",
  "body": "Se registró una infracción",
  "data": {
    "type": "infraction",
    "infractionId": "inf_345678"
  }
}
```

#### **Comunicados**

```json
{
  "title": "Nuevo comunicado",
  "body": "Comunicado de la administración",
  "data": {
    "type": "announcement"
  }
}
```

> 📖 **Documentación Detallada**: Ver [docs/NOTIFICATION_NAVIGATION.md](docs/NOTIFICATION_NAVIGATION.md) para información completa sobre navegación automática.

## 🧪 Testing

### Testing Manual

1. **Permisos**: Ve a Configuración → Notificaciones y activa las push notifications
2. **Token**: Verifica que se genere un token de push
3. **Registro**: Confirma que el token se registre en el backend
4. **Notificaciones Push**: Envía notificaciones desde el backend usando el token
5. **Navegación**: Toca las notificaciones para probar la navegación automática

### Debugging

```typescript
// Ver el token generado
console.log("Push token:", expoPushToken);

// Ver permisos
console.log("Has permissions:", hasPermissions);

// Ver última notificación recibida
console.log("Last notification:", lastNotification);
```

## 🔧 Configuración del Backend

### Endpoints Requeridos

Tu backend debe tener estos endpoints configurados:

```
POST /mobile/push-token
DELETE /mobile/push-token/:id
```

### Envío de Notificaciones Push

Para enviar notificaciones desde el backend:

```javascript
// Ejemplo con Node.js
const { Expo } = require("expo-server-sdk");
const expo = new Expo();

const messages = [
  {
    to: "ExponentPushToken[...]",
    sound: "default",
    title: "Título de la notificación",
    body: "Mensaje de la notificación",
    data: { screen: "Property", propertyId: "123" },
  },
];

const chunks = expo.chunkPushNotifications(messages);
for (let chunk of chunks) {
  await expo.sendPushNotificationsAsync(chunk);
}
```

## 📱 Configuración de Assets

Necesitas crear estos archivos en `src/assets/`:

- `notification-icon.png` (96x96px, transparente)
- `notification-sound.wav` (opcional, sonido personalizado)

## 🔍 Debugging

### Logs Útiles

```typescript
console.log("Push token:", expoPushToken);
console.log("Has permissions:", hasPermissions);
console.log("Last notification:", lastNotification);
```

### Problemas Comunes

1. **Token no se genera**: Verificar `easProjectId` en configuración
2. **Permisos denegados**: Solicitar permisos manualmente
3. **Notificaciones no aparecen**: Verificar que la app esté en segundo plano

## 🎯 Próximos Pasos

1. **Navegación**: Implementar navegación automática basada en notificaciones
2. **Categorías**: Agregar diferentes tipos de notificaciones
3. **Programación**: Notificaciones recurrentes para recordatorios
4. **Analytics**: Tracking de interacciones con notificaciones

## 📚 Documentación Adicional

- Ver `docs/PUSH_NOTIFICATIONS.md` para documentación técnica completa
- Consultar [Expo Notifications Docs](https://docs.expo.dev/versions/latest/sdk/notifications/)
- Revisar [Expo Push Notifications Guide](https://docs.expo.dev/push-notifications/overview/)

---

**¡Las notificaciones push están listas para usar! 🎉**

Puedes empezar a probar inmediatamente usando los botones de prueba en el Dashboard o configurando notificaciones desde la pantalla de configuración.
