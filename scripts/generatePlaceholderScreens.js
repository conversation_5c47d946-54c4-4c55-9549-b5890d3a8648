const fs = require('fs');
const path = require('path');

// Template para pantallas placeholder
const createScreenTemplate = (componentName, title, subtitle) => `import React from "react";
import { StyleSheet, Text, View, ScrollView } from "react-native";
import { GradientView } from "../../components/layouts/GradientView";
import { theme } from "../../theme";

export const ${componentName}: React.FC = () => {
  return (
    <GradientView firstLineText="${title}" secondLineText="${subtitle}">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.placeholderContainer}>
          <Text style={styles.placeholderText}>
            ${title} ${subtitle}
          </Text>
          <Text style={styles.placeholderSubtext}>
            Esta pantalla será implementada próximamente
          </Text>
        </View>
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 100,
  },
  placeholderText: {
    fontSize: 18,
    fontWeight: "bold",
    color: theme.colors.text,
    textAlign: "center",
    marginBottom: 8,
  },
  placeholderSubtext: {
    fontSize: 14,
    color: theme.colors.gray600,
    textAlign: "center",
  },
});`;

// Pantallas de Security que faltan
const securityScreens = [
  { name: 'SecurityPackageDetailScreen', title: 'Detalle', subtitle: 'de Paquete' },
  { name: 'ReceivePackageScreen', title: 'Recibir', subtitle: 'Paquete' },
  { name: 'DeliverPackageScreen', title: 'Entregar', subtitle: 'Paquete' },
  { name: 'IncidentsListScreen', title: 'Incidentes', subtitle: 'Lista' },
  { name: 'IncidentDetailScreen', title: 'Detalle', subtitle: 'de Incidente' },
  { name: 'CreateIncidentScreen', title: 'Crear', subtitle: 'Incidente' },
];

// Pantallas de Manager
const managerScreens = [
  { name: 'ManagerDashboardScreen', title: 'Dashboard', subtitle: 'Administración' },
  { name: 'AnalyticsScreen', title: 'Analíticas', subtitle: 'Generales' },
  { name: 'ManagerPropertiesListScreen', title: 'Propiedades', subtitle: 'Lista' },
  { name: 'ManagerPropertyDetailScreen', title: 'Detalle', subtitle: 'de Propiedad' },
  { name: 'PropertyAnalyticsScreen', title: 'Analíticas', subtitle: 'de Propiedad' },
  { name: 'ManagerResidentsListScreen', title: 'Residentes', subtitle: 'Lista' },
  { name: 'ManagerResidentDetailScreen', title: 'Detalle', subtitle: 'de Residente' },
  { name: 'AddResidentScreen', title: 'Agregar', subtitle: 'Residente' },
  { name: 'EditResidentScreen', title: 'Editar', subtitle: 'Residente' },
  { name: 'ManagerMaintenanceListScreen', title: 'Mantenimiento', subtitle: 'Lista' },
  { name: 'ManagerMaintenanceDetailScreen', title: 'Detalle', subtitle: 'de Mantenimiento' },
  { name: 'AssignMaintenanceScreen', title: 'Asignar', subtitle: 'Mantenimiento' },
  { name: 'MaintenanceAnalyticsScreen', title: 'Analíticas', subtitle: 'de Mantenimiento' },
  { name: 'ManagerReportsListScreen', title: 'Reportes', subtitle: 'Lista' },
  { name: 'FinancialReportsScreen', title: 'Reportes', subtitle: 'Financieros' },
  { name: 'OccupancyReportsScreen', title: 'Reportes', subtitle: 'de Ocupación' },
  { name: 'MaintenanceReportsScreen', title: 'Reportes', subtitle: 'de Mantenimiento' },
  { name: 'ExportReportsScreen', title: 'Exportar', subtitle: 'Reportes' },
];

// Crear directorio Security si no existe
const securityDir = path.join(__dirname, '../src/screens/Security');
if (!fs.existsSync(securityDir)) {
  fs.mkdirSync(securityDir, { recursive: true });
}

// Crear directorio Manager si no existe
const managerDir = path.join(__dirname, '../src/screens/Manager');
if (!fs.existsSync(managerDir)) {
  fs.mkdirSync(managerDir, { recursive: true });
}

// Generar pantallas de Security
securityScreens.forEach(screen => {
  const filePath = path.join(securityDir, `${screen.name}.tsx`);
  if (!fs.existsSync(filePath)) {
    const content = createScreenTemplate(screen.name, screen.title, screen.subtitle);
    fs.writeFileSync(filePath, content);
    console.log(`Created: ${screen.name}.tsx`);
  }
});

// Generar pantallas de Manager
managerScreens.forEach(screen => {
  const filePath = path.join(managerDir, `${screen.name}.tsx`);
  if (!fs.existsSync(filePath)) {
    const content = createScreenTemplate(screen.name, screen.title, screen.subtitle);
    fs.writeFileSync(filePath, content);
    console.log(`Created: ${screen.name}.tsx`);
  }
});

console.log('Placeholder screens generation completed!');
